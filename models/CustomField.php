<?php

namespace models;

use models\composite\oFileDoc\saveNewDocumentType;
use models\constants\gl\glUserRole;
use models\Controllers\backoffice\DocsForm;
use models\Controllers\backoffice\LMRequest;
use models\lendingwise\tblCustomField;
use models\lendingwise\tblCustomFieldType;
use models\lendingwise\tblCustomFieldValue;
use models\lendingwise\tblSectionHeadingMaster;
use models\types\strongType;

class CustomField extends strongType
{
    public static bool $isJSManaged = false;
    /**
     * @var tblCustomFieldType[]
     */
    public static ?array $Types = null;

    /**
     * @var tblSectionHeadingMaster[]
     */
    public static array $SectionHeadings;
    public static ?array $saved = null;
    public static ?string $LMRId = null;

    public static function Init()
    {
        self::$Types = tblCustomFieldType::GetAll(null, ['type' => 'ASC']);
        self::$SectionHeadings = tblSectionHeadingMaster::GetAll([
            'phpFile' => '<> NULL',
        ], [
            'sectionHeading' => 'ASC',
        ]);
    }


    public static function Save(
        string  $objectType,
        string  $primaryKey,
        int     $PCID,
        ?int    $createdBy,
        ?string $createdByRole,
        ?string $createdByGroup
    )
    {
        if (!$primaryKey) {
            return;
        }

        if (!$objectType) {
            return;
        }

        if (!$PCID) {
            return;
        }

        self::$saved = $_REQUEST['CustomField'] ?? [];
        self::$LMRId = $_REQUEST['LMRId'] ?? null;
        //$_REQUEST['LMRId'] is 0 when creating a new loan file
        if (!self::$LMRId) {
            self::$LMRId = LMRequest::File()->LMRId;
        }
        self::saveFileUpload();
        if (empty(self::$saved)) {
            return;
        }

        $customFields = tblCustomField::GetAll([
            'PCID' => $PCID,
        ]);

        foreach ($customFields as $item) {
            if (!isset(self::$saved[$item->id])) {
                continue;
            }
            $item->saveEntry(
                $objectType,
                $primaryKey,
                self::$saved[$item->id],
                $createdBy,
                $createdByRole,
                $createdByGroup
            );
        }
    }

    public static function saveFileUpload(): void
    {

        if (isset($_FILES['CustomField']['name']) && is_array($_FILES['CustomField']['name'])) {
            foreach ($_FILES['CustomField']['name'] as $key => $originalName) {
                $fileTmpName = $_FILES['CustomField']['tmp_name'][$key];
                $fileType = $_FILES['CustomField']['type'][$key];
                $fileError = $_FILES['CustomField']['error'][$key];
                $fileSize = $_FILES['CustomField']['size'][$key];

                if ($fileTmpName && $originalName && $fileSize && $fileError === 0) {
                    $fileName = trim('/customField/File/' . $key . '/' . $originalName);
                    $tblFileStorage = FileStorage::moveFile($fileTmpName, $fileName);
                    if ($tblFileStorage) {
                        self::$saved[$key] = $tblFileStorage->id;

                        $tblCustomField = tblCustomField::Get(['id' => $key]);
                        $documentName = str_replace('', '', $tblCustomField->Label);//.'(Custom Field)';

                        $otherDocumentId = self::createOtherDocumentForCustomFieldFile(self::$LMRId, $documentName);
                        DocsForm::copyStorageFile(self::$LMRId,
                            $tblFileStorage->id,
                            $otherDocumentId . '_' . $documentName,
                            $documentName,
                            '',
                            'custom',
                            $documentName,
                            ' (Custom Field File Upload)'
                        );
                    }
                }
            }
        }
    }

    public static function createOtherDocumentForCustomFieldFile(int    $LMRId,
                                                                 string $docTypeName): ?int
    {

        return saveNewDocumentType::getReport([
            'LMRId'        => $LMRId,
            'userId'       => PageVariables::$userNumber,
            'docType'      => 'other',
            'docTypeValue' => $docTypeName,
            'requiredBy'   => [
                'Borrower',
                'Branch',
                'Broker',
                'Loan Officer',
            ],
            'userGroup'    => PageVariables::$userGroup,
        ]);

    }

    public static function RenderForTabSection(
        ?int    $PCID,
        string  $objectType,
        ?string $primaryKey,
        string  $sectionID,
        ?string $opt = null,
        ?string $activeTab = null,
        ?array  $fileTypes = null,
        ?array  $loanPrograms = null
    ): string
    {
        // if (self::$isJSManaged || (stristr(Server::RequestURI(), 'HMLOWebForm.php') !== false && !$primaryKey)) {
        if (!$primaryKey) {
            // public web form handles this in JS, not PHP
            $fileTypes = null;
            $loanPrograms = null;
        }
        if (PageVariables::$userRole === glUserRole::SUPER) {
            $PCID = LMRequest::File()->FPCID;
        }
        if (!$PCID) {
            return '';
        }
        $customFields = tblCustomField::GetAll([
            'PCID'       => $PCID,
            'objectType' => $objectType,
            'sectionID'  => $sectionID,
            'isActive'   => 1,
        ], [
            'DisplayOrder' => 'ASC',
            'Label'        => 'ASC',
        ]);

        $responses = [];

        if ($primaryKey) {
            $res = tblCustomFieldValue::GetAll([
                'objectType' => $objectType,
                'primaryKey' => $primaryKey,
            ]);

            foreach ($res as $item) {
                $responses[$item->tblCustomFieldId] = $item->getTblCustomField_by_id()->tblCustomFieldTypeId === tblCustomFieldType::File ? $item->getTblFileStorage_by_fileStorageId()->givenPath : json_decode($item->userEntered);
            }
        }

        $html = '<div class="row">';
        foreach ($customFields as $field) {

            $isFieldMatchesFileType = true;
            $isFieldMatchesLoanProgram = true;
            if ($primaryKey) {
                if ($fileTypes && sizeof($fileTypes)) {
                    if ($field->_fileTypes() && sizeof($field->_fileTypes())) {
                        if (!array_intersect($fileTypes, $field->_fileTypes())) {
                            $isFieldMatchesFileType = false;
                            //  continue;
                        }
                    }
                }

                if ($loanPrograms && sizeof($loanPrograms)) {
                    if ($field->_loanPrograms() && sizeof($field->_loanPrograms())) {
                        if (!array_intersect($loanPrograms, $field->_loanPrograms())) {
                            $isFieldMatchesLoanProgram = false;
                            // continue;
                        }
                    }
                }
            }

            $display = $field->displayQA + $field->displayFA + $field->displayBO;
            switch ($opt) {
                case 'QA':
                    if ($display && !$field->displayQA) {
                        continue 2;
                    }
                    $mandatory = $field->mandatoryQA;
                    break;
                case 'FA':
                    if ($display && !$field->displayFA) {
                        continue 2;
                    }
                    $mandatory = $field->mandatoryFA;
                    break;
                default:
                    if ($display && !$field->displayBO) {
                        continue 2;
                    }
                    $mandatory = $field->mandatoryBO;
            }
            $isFieldAllowToDisplay = $isFieldMatchesFileType && $isFieldMatchesLoanProgram && $display;
            $html .= $field->render(
                $responses[$field->id] ?? null,
                $mandatory,
                false,
                $fileTypes,
                $loanPrograms,
                $isFieldAllowToDisplay
            );
        }
        return $html . '</div>';
    }
}
