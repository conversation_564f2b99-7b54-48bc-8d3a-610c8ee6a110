<?php
global $sameAsEntityAddr, $borrowerUnderEntity, $isClientProfile, $fileTab, $fieldsInfo,
       $HMLOLoanInfoSectionsDisp, $allowToEdit, $tabIndex, $hideThisField, $entityName,
       $CBEID, $tradeName, $entityType, $dateOfFormation,
       $entityStateOfFormation, $stateArray, $ENINo, $organizationalRef, $businessPhone,
       $entityWebsite, $startDateAtLocation, $entityAddress, $entityCity, $entityState,
       $entityZip, $entityBillAddrChk, $entityBillAddress, $entityBillCity, $entityBillState,
       $entityBillZip, $entityLocation, $tabIndexNo, $entityPropertyOwnerShip,
       $valueOfProperty, $totalDebtOnProperty, $nameOfLenders, $landlordMortagageContactName,
       $landlordMortagagePhone, $rentMortagagePayment, $showBorrowerEntityDispOpt, $noOfEmployees,
       $noOfEmployeesAfterLoan, $businessCategory, $productTypeOrServiceSold,
       $naicsCode, $entityService, $entityProduct, $entityB2B, $entityB2C, $benBusinessHomeBased,
       $benCreditCardPayments, $terminalOrMakeModel,
       $benCardProcessorBank, $merchantProcessingBankName, $benChargeSalesTax,
       $benEmployeesPaid, $benBusinessLocation, $benHowManyLocation,
       $benOtherLocation, $benBusinessFranchise, $benNameOfFranchise, $benPointOfContact,
       $benPointOfContactPhone, $benPointOfContactEmail, $benWebsiteForFranchise, $isBusinessSeasonal,
       $isBusinessSeasonalPeakMonth, $businessDescription, $businessReference, $fileHMLOEntityRefInfoArray,
       $avgMonthlyCreditcardSale, $avgTotalMonthlySale, $annualGrossSales, $annualGrossProfit,
       $ordinaryBusinessIncome, $dateOfOperatingAgreement, $grossAnnualRevenues, $grossIncomeLastYear,
       $netIncomeLastYear, $grossIncome2YearsAgo, $netIncome2YearsAgo, $averageBankBalance,
       $isBorrowerProfile, $fileHMLOEntityInfo, $fileMemberOfficerInfo, $entityNotes, $corporateSecretaryName, $PCID,
       $minTimeInBusiness, $recentNSFs, $hasBusinessBankruptcy,
       $businessBankruptcy, $HMLOPCBasicMinSeasoningBusinessBankruptcyInfoArray,
       $personalBankruptcy, $HMLOPCBasicEntitityStateFormationInfoArray,
       $HMLOPCBasicMinTimeInBusinessInfoArray, $statesRegisterdIn, $businessType, $HMLOPCBasicEntityTypeInfoArray,
       $trustType, $retirementEntity, $borrowerType;
global $fileMC;
global $CID;
use models\composite\oFile\getFileEntityMembers;
use models\constants\borrowerBusinessEntity;
use models\constants\gl\glBenEmployeesPaid;
use models\constants\gl\glbusinessCategoryArray;
use models\constants\gl\glBusinessType;
use models\constants\gl\glEntityTypeArray;
use models\constants\gl\globalBankruptcyCat;
use models\constants\gl\globalBusinessBankruptcyCat;
use models\constants\gl\globalMinTimeInBusinessCat;
use models\constants\gl\globalRecentNSFsCat;
use models\constants\gl\glPCID;
use models\constants\gl\glPropTypeArray;
use models\constants\gl\glRetirementEntityArray;
use models\constants\gl\glTrustTypeArray;
use models\constants\gl\glBorrowerType;
use models\Controllers\backoffice\borrower\Docs;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\entityMembers;
use models\Controllers\loanForm;
use models\cypher;
use models\CustomField;
use models\lendingwise\tblFile;
use models\lendingwise\tblEntityMembers;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use pages\backoffice\borrowerProfile\borrowerProfile;

$glPropTypeArray = glPropTypeArray::$glPropTypeArray;
$globalMinTimeInBusinessCat = globalMinTimeInBusinessCat::$globalMinTimeInBusinessCat;
$globalRecentNSFsCat = globalRecentNSFsCat::$globalRecentNSFsCat;
$globalBankruptcyCat = globalBankruptcyCat::$globalBankruptcyCat;
$globalBusinessBankruptcyCat = globalBusinessBankruptcyCat::$globalBusinessBankruptcyCat;
$glEntityTypeArray = glEntityTypeArray::$glEntityTypeArray;
$glTrustTypeArray = glTrustTypeArray::$glTrustTypeArray;
$glRetirementEntityArray = glRetirementEntityArray::$glRetirementEntityArray;
$glbusinessCategoryArray = glbusinessCategoryArray::$glbusinessCategoryArray;
$glBorrowerTypeArray = glBorrowerType::$glBorrowerTypeArray;

if ($borrowerUnderEntity == 'Yes') {
    $sameAsEntityDisOption = ' display : flex; ';
} else {
    $sameAsEntityDisOption = ' display : none; ';
}
if ($isClientProfile) $borrowerUnderEntity = 'Yes';
$operatingStates = Strings::showField('operatingStates', 'fileHMLOEntityInfo') ?? '';

$secArr = BaseHTML::sectionAccess2(['sId' => 'BEN', 'opt' => $fileTab]); // Get Active Fields only...
loanForm::pushSectionID('BEN');

//Get the Business Entity Members Information
//Borrower Profile
if ($isBorrowerProfile) {
    $parentCount = [];
} else {
//parent count
    $parentCount = getFileEntityMembers::getRootMembers(LMRequest::$LMRId ?: 0);
}
?>
<!-- Business Entity Section Start -->
<input type="hidden" name="encCID" id="encCID" value="">
<input type="hidden" name="encCBEID" id="encCBEID" value="">
<input type="hidden" name="encPCID" id="encPCID" value="">
<input type="hidden" name="entitySectionID" id="entitySectionID" value="">
<?php if ($isBorrowerProfile) { ?>
<input type="hidden" name="allowNestedEntityMembers" id="allowNestedEntityMembers" value="">
<?php } ?>
<div class="card card-custom  HMLOLoanInfoSections isClientInfo isClientInfoCard BEN <?php if (count(Arrays::getValueFromArray('BEN', $fieldsInfo)) <= 0) {
    echo 'secHide';
} ?>" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">

    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('BEN'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('BEN')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('BEN'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar ">
            <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                  data-card-tool="toggle"
                  data-section="isClientInfoCard"
                  data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>

            <span class="cursor-pointer btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <a class="cursor-pointer btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body isClientInfoCard_body">
        <div class="row ">
            <div class="col-md-6 borrowerType_disp <?php echo loanForm::showField('borrowerType'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('borrowerType', 'font-weight-bold col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select
                                data-loc="businessEntitySection.php"
                                name="borrowerType"
                                class="form-control input-sm preVal"
                                id="borrowerType"
                                onchange="showHideBusinessEntityFields(this.value);LMRequest.lastUpdatedParams('BorrowerType');"
                                tabindex="<?php echo $tabIndex++; ?>"
                                orig-value="<?php echo $retirementEntity; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                foreach ($glBorrowerTypeArray as $i => $glBorrowerType) {
                                    $glBorrowerType = trim($glBorrowerType);
                                    $sOpt = Arrays::isSelected($glBorrowerType, $borrowerType);
                                    echo "<option value=\"" . $glBorrowerType . "\" " . $sOpt . '>' . $glBorrowerType . '</option>';
                                }

                                ?>
                            </select>
                        <?php } else { ?><b><?php echo $borrowerType; ?></b><?php } ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6  borrowerType_disp trustType_disp <?php echo loanForm::showField('trustType');
            if ($borrowerType != glBorrowerType::BORROWER_TYPE_TRUST) {
                echo ' d-none ';
            } ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('trustType', 'col-md-5', '', '', '', 'Trust Type'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select
                                data-loc="businessEntitySection.php"
                                name="trustType"
                                class="form-control input-sm preVal"
                                id="trustType"
                                tabindex="<?php echo $tabIndex++; ?>"
                                orig-value="<?php echo $trustType; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                foreach ($glTrustTypeArray as $i => $glTrust) {
                                    $glTrust = trim($glTrust);
                                    $sOpt = Arrays::isSelected($glTrust, $trustType);
                                    echo "<option value=\"" . $glTrust . "\" " . $sOpt . '>' . $glTrust . '</option>';
                                }

                                ?>
                            </select>
                        <?php } else { ?><b><?php echo $trustType; ?></b><?php } ?>
                    </div>
                </div>
            </div>

            <div class="col-md-6  borrowerType_disp retirementEntity_disp <?php echo loanForm::showField('retirementEntity');
            if ($borrowerType != glBorrowerType::BORROWER_TYPE_RETIREMENT_ENTITY) {
                echo ' d-none ';
            }
            ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('retirementEntity', 'col-md-5', '', '', '', 'Retirement Entity'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select
                                data-loc="businessEntitySection.php"
                                name="retirementEntity"
                                class="form-control input-sm preVal"
                                id="retirementEntity"
                                tabindex="<?php echo $tabIndex++; ?>"
                                orig-value="<?php echo $retirementEntity; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                foreach ($glRetirementEntityArray as $i => $glRetirementEntity) {
                                    $glRetirementEntity = trim($glRetirementEntity);
                                    $sOpt = Arrays::isSelected($glRetirementEntity, $retirementEntity);
                                    echo "<option value=\"" . $glRetirementEntity . "\" " . $sOpt . '>' . $glRetirementEntity . '</option>';
                                }

                                ?>
                            </select>
                        <?php } else { ?><b><?php echo $trustType; ?></b><?php } ?>
                    </div>
                </div>
            </div>
        </div>
        <?php if (trim(BaseHTML::fieldAccess(['fNm' => 'borrowerType', 'sArr' => $secArr, 'opt' => 'D'])) == 'secHide') {
            $showBorrowerEntityDispOpt = '';
            $borrowerType = 'Entity';
            ?>
            <input type="hidden" name="borrowerType" value="<?php echo glBorrowerType::BORROWER_TYPE_ENTITY; ?>">
            <?php
        } ?>
        <div class="row">
            <?php
            /*story - 27792 populate the parent question for entity field*/
            if (!$isClientProfile && ($PCID == glPCID::PCID_G1CommMort)) { ?>
                <div class=" col-md-12 borrowerUnderEntity_disp <?php echo loanForm::showField('borrowerUnderEntity'); ?>">
                    <div class="form-group row">
                        <?php echo loanForm::label('borrowerUnderEntity', 'font-weight-bold col-md-3 col-sm-3'); ?>
                        <div class="col-md-2 col-sm-5 col-xs-7">
                            <?php if ($allowToEdit) { ?>
                                <input type="radio"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerUnderEntity', 'sArr' => $secArr, 'opt' => 'M']); ?> entityDis"
                                       name="borrowerUnderEntity" value="Yes" id="borrowerUnderEntityYes"
                                       onclick="showHideEntityFields(this.value)"
                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $borrowerUnderEntity); ?>
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerUnderEntity', 'sArr' => $secArr, 'opt' => 'I']); ?>>Yes
                                <input type="radio" name="borrowerUnderEntity" id="borrowerUnderEntity" value="No"
                                       onclick="showHideEntityFields(this.value)"
                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $borrowerUnderEntity); ?>
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerUnderEntity', 'sArr' => $secArr, 'opt' => 'I']); ?>>No
                            <?php } else { ?><b><?php echo $borrowerUnderEntity; ?></b><?php } ?>
                        </div>
                    </div>
                </div>

                <?php
                if (trim(BaseHTML::fieldAccess(['fNm' => 'borrowerUnderEntity', 'sArr' => $secArr, 'opt' => 'D'])) == 'secHide') {
                    $showBorrowerEntityDispOpt = 'display:none';
                }
            } ?>

            <div class="col-md-12">
            <?php echo CustomField::RenderForTabSection(
                PageVariables::$PCID,
                tblFile::class,
                LMRequest::$LMRId,
                'BEN',
                $fileTab,
                $activeTab,
                LMRequest::myFileInfo()->getFileTypes(),
                LMRequest::myFileInfo()->getLoanPrograms()
            ); ?>
            </div>

            <div class="col-md-12 borrowerEntityDetails entityField  px-0 mx-0"
                 style="<?php echo $showBorrowerEntityDispOpt; ?>">

                <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="BenGbiTitle">
                    <label class="font-weight-bold bg-secondary  py-2  col-lg-12"><b><?php echo BaseHTML::getSubSectionHeading('BEN', 'genBusinessInfoSubSection'); ?></b></label>
                </div>

                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenGbiTitle entityName_disp <?php echo loanForm::showField('entityName'); ?>">
                        <?php echo loanForm::label('entityName', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <span class="entitySec">
                                    <div class="input-group">
                                        <input type="text"
                                               class="form-control input-sm preVal entityName <?php echo BaseHTML::fieldAccess(['fNm' => 'entityName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="entityName"
                                               id="entityName" <?php if (Strings::showField('clientId', 'LMRInfo') > 0) { ?> onclick="populateContactName('Entity', this.value)" <?php } ?>
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               value="<?php echo htmlspecialchars($entityName); ?>"
                                               orig-value="<?php echo $entityName; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <div class="input-group-append popoverClass"
                                             onclick="removeClientEntityInfo();"
                                             data-content="Click to remove business entity details">
                                            <span class="input-group-text ">
                                                <i class="fa fa-times text-danger"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <input type="hidden" name="CBEID" id="CBEID" value="<?php echo $CBEID; ?>">
				                </span>
                            <?php } else { ?><b><?php echo $entityName; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>


                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenGbiTitle tradeName_disp <?php echo loanForm::showField('tradeName'); ?>">
                        <?php echo loanForm::label('tradeName', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'tradeName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text" name="tradeName" id="tradeName" tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlspecialchars($tradeName); ?>" size="10" maxlength="75"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'tradeName', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo $tradeName; ?>">
                            <?php } else {
                                echo $tradeName;
                            } ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenGbiTitle entityType_disp <?php echo loanForm::showField('entityType'); ?>">
                        <?php echo loanForm::label('entityType', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <select
                                    data-loc="businessEntitySection.php"
                                    name="entityType"
                                    class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    id="entityType"
                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityType', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                    orig-value="<?php echo $entityType; ?>">
                                    <option value=""> - Select -</option>
                                    <?php
                                    if (count($HMLOPCBasicEntityTypeInfoArray ?? []) > 0) {
                                        foreach ($HMLOPCBasicEntityTypeInfoArray as $HMLOPCBasicEntityType) {
                                            $isSelected = $HMLOPCBasicEntityType == $entityType ? 'selected' : '';
                                            ?>
                                            <option value="<?php echo $HMLOPCBasicEntityType; ?>" <?php echo $isSelected; ?>><?php echo $HMLOPCBasicEntityType; ?></option>
                                        <?php }
                                    } else {
                                        foreach ($glEntityTypeArray as $i => $glEntity) {
                                            $glEntity = trim($glEntity);
                                            $sOpt = Arrays::isSelected($glEntity, $entityType);
                                            echo "<option value=\"" . $glEntity . "\" " . $sOpt . '>' . $glEntity . '</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            <?php } else { ?><b><?php echo $entityType; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div class="form-group row col-md-6 BenGbiTitle dateOfFormation_disp <?php echo loanForm::showField('dateOfFormation'); ?>">
                    <?php echo loanForm::label('dateOfFormation', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend dateOfFormation">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'dateOfFormation', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass "
                                       placeholder="MM/DD/YYYY" type="text" name="dateOfFormation" id="dateOfFormation"
                                       value="<?php echo $dateOfFormation ?>" tabindex="<?php echo $tabIndex++; ?>"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'dateOfFormation', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo $dateOfFormation; ?>"/>
                            </div>
                        <?php } else { ?><b><?php echo $dateOfFormation ?></b><?php } ?>
                    </div>
                </div>

                <div class="form-group row col-md-6 BenGbiTitle minTimeInBusiness_disp <?php echo loanForm::showField('minTimeInBusiness'); ?>">
                    <?php echo loanForm::label('minTimeInBusiness', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select name="minTimeInBusiness"
                                    id="minTimeInBusiness"
                                    tabindex="<?php echo $tabIndex++; ?>"
                                    class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'minTimeInBusiness', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    data-placeholder="Select Min Time In Business">
                                <option></option>
                                <?php
                                foreach ($globalMinTimeInBusinessCat as $eachMinTimeInBusinessID => $eachMinTimeInBusinessVal) { ?>
                                    <option value="<?php echo $eachMinTimeInBusinessID; ?>" <?php if ($minTimeInBusiness == $eachMinTimeInBusinessID) {
                                        echo 'selected';
                                    } ?>><?php echo $eachMinTimeInBusinessVal; ?></option>
                                <?php } ?>
                            </select>
                        <?php } else { ?><b><?php echo $globalMinTimeInBusinessCat[$minTimeInBusiness] ?></b><?php } ?>
                    </div>
                </div>

                <div class="form-group row col-md-6 BenGbiTitle entityStateOfFormation_disp <?php echo loanForm::showField('entityStateOfFormation'); ?>">
                    <?php echo loanForm::label('entityStateOfFormation', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityStateOfFormation', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="entityStateOfFormation"
                                    id="entityStateOfFormation"
                                    tabindex="<?php echo $tabIndex++; ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'entityStateOfFormation', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                    orig-value="<?php echo $entityStateOfFormation; ?>">
                                <option value=''> - Select -</option>
                                <?php
                                if (count($HMLOPCBasicEntitityStateFormationInfoArray ?? []) > 0) {
                                    for ($j = 0; $j < count($stateArray); $j++) {
                                        if (in_array(trim($stateArray[$j]['stateCode']), $HMLOPCBasicEntitityStateFormationInfoArray)) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $entityStateOfFormation);
                                            echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                        }
                                    }
                                } else {
                                    for ($j = 0; $j < count($stateArray); $j++) {
                                        $sOpt = '';
                                        $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $entityStateOfFormation);
                                        echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        <?php } else { ?><b><?php echo $entityStateOfFormation; ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenGbiTitle statesRegisterdIn_disp <?php echo loanForm::showField('statesRegisterdIn'); ?>">
                    <?php echo loanForm::label('statesRegisterdIn', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="hidden" value="<?php echo $statesRegisterdIn; ?>"
                                   name="statesRegisterdInHidden"
                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'statesRegisterdIn', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'statesRegisterdIn', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            <select class="chzn-select form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'statesRegisterdIn', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="statesRegisterdIn[]"
                                    id="statesRegisterdIn"
                                    multiple
                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'statesRegisterdIn', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                    orig-value="<?php echo $statesRegisterdIn; ?>"
                                    data-placeholder="Please Select States">
                                <?php
                                if ($statesRegisterdIn != '') {
                                    $statesRegisterdInArray = explode(',', $statesRegisterdIn);
                                }
                                for ($j = 0; $j < count($stateArray); $j++) {
                                    $sOpt = '';
                                    if (in_array(trim($stateArray[$j]['stateCode']), $statesRegisterdInArray ?? [])) {
                                        $sOpt = ' selected ';
                                    }
                                    echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                }

                                ?>
                            </select>
                        <?php } else { ?><b><?php echo $statesRegisterdIn; ?></b><?php } ?>
                    </div>
                </div>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6  BenGbiTitle ENINo_disp <?php echo loanForm::showField('ENINo'); ?>">
                        <?php echo loanForm::label('ENINo', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm mask_ein preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'ENINo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       onblur="fieldValidation(this.id,this.name)"
                                       type="text" name="ENINo"
                                       id="ENINo"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo $ENINo; ?>"
                                       size="20"
                                       maxlength="30"
                                       placeholder="__ - _______"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'ENINo', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo $ENINo; ?>">
                            <?php } else { ?><b><?php echo $ENINo; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>

                <div class="form-group row col-md-6  BenGbiTitle borLicenseNumber_disp <?php echo loanForm::showField('borLicenseNumber'); ?>">
                    <?php echo loanForm::label('borLicenseNumber', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borLicenseNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text"
                                   name="borLicenseNumber"
                                   id="borLicenseNumber"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo htmlentities(Strings::showField('borLicenseNumber', 'fileHMLOEntityInfo')); ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borLicenseNumber', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                   orig-value="<?php echo Strings::showField('borLicenseNumber', 'fileHMLOEntityInfo'); ?>">
                        <?php } else { ?>
                            <b><?php echo Strings::showField('borLicenseNumber', 'fileHMLOEntityInfo'); ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenGbiTitle borLicenseIssuance_disp <?php echo loanForm::showField('borLicenseIssuance'); ?>">
                    <?php echo loanForm::label('borLicenseIssuance', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend borLicenseIssuance">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm preVal date_mask <?php echo BaseHTML::fieldAccess(['fNm' => 'borLicenseIssuance', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                       placeholder="MM/DD/YYYY"
                                       type="text"
                                       name="borLicenseIssuance"
                                       id="borLicenseIssuance"
                                       value="<?php echo Dates::formatDateWithRE(Strings::showField('borLicenseIssuance', 'fileHMLOEntityInfo'), 'YMD', 'm/d/Y') ?>"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borLicenseIssuance', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo Dates::formatDateWithRE(Strings::showField('borLicenseIssuance', 'fileHMLOEntityInfo'), 'YMD', 'm/d/Y'); ?>"/>
                            </div>
                        <?php } else { ?>
                            <b><?php echo Dates::formatDateWithRE(Strings::showField('borLicenseIssuance', 'fileHMLOEntityInfo'), 'YMD', 'm/d/Y'); ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenGbiTitle borLicenseExpiration_disp <?php echo loanForm::showField('borLicenseExpiration'); ?>">
                    <?php echo loanForm::label('borLicenseExpiration', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend borLicenseExpiration">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm preVal date_mask <?php echo BaseHTML::fieldAccess(['fNm' => 'borLicenseExpiration', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                       placeholder="MM/DD/YYYY" type="text" name="borLicenseExpiration"
                                       id="borLicenseExpiration"
                                       value="<?php echo Dates::formatDateWithRE(Strings::showField('borLicenseExpiration', 'fileHMLOEntityInfo'), 'YMD', 'm/d/Y') ?>"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borLicenseExpiration', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo Dates::formatDateWithRE(Strings::showField('borLicenseExpiration', 'fileHMLOEntityInfo'), 'YMD', 'm/d/Y'); ?>"/>
                            </div>
                        <?php } else { ?>
                            <b><?php echo Dates::formatDateWithRE(Strings::showField('borLicenseExpiration', 'fileHMLOEntityInfo'), 'YMD', 'm/d/Y'); ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenGbiTitle statesLicIssued_disp <?php echo loanForm::showField('statesLicIssued'); ?>">
                    <?php echo loanForm::label('statesLicIssued', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'statesLicIssued', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="statesLicIssued" id="statesLicIssued"
                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'statesLicIssued', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                    orig-value="<?php echo Strings::showField('statesLicIssued', 'fileHMLOEntityInfo'); ?>">
                                <option value=''> - Select -</option>
                                <?php
                                if (count($HMLOPCBasicEntitityStateFormationInfoArray ?? []) > 0) {
                                    for ($j = 0; $j < count($stateArray); $j++) {
                                        if (in_array(trim($stateArray[$j]['stateCode']), $HMLOPCBasicEntitityStateFormationInfoArray)) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), Strings::showField('statesLicIssued', 'fileHMLOEntityInfo'));
                                            echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                        }
                                    }
                                } else {
                                    for ($j = 0; $j < count($stateArray); $j++) {
                                        $sOpt = '';
                                        $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), Strings::showField('statesLicIssued', 'fileHMLOEntityInfo'));
                                        echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <b><?php echo Strings::showField('statesLicIssued', 'fileHMLOEntityInfo'); ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenGbiTitle operatingStates_disp <?php echo loanForm::showField('operatingStates'); ?>">
                    <?php echo loanForm::label('operatingStates', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="hidden" value="<?php echo $operatingStates; ?>"
                                   name="operatingStatesHidden"
                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'operatingStates', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'operatingStates', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            <select class="chzn-select form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'operatingStates', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="operatingStates[]" id="operatingStates" multiple
                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'operatingStates', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                    orig-value="<?php echo $operatingStates; ?>"
                                    data-placeholder="Please Select States">
                                <?php
                                if ($operatingStates != '') {
                                    $operatingStatesArray = explode(',', $operatingStates);
                                }
                                for ($j = 0; $j < count($stateArray); $j++) {
                                    $sOpt = '';
                                    if (in_array(trim($stateArray[$j]['stateCode']), $operatingStatesArray ?? [])) {
                                        $sOpt = ' selected ';
                                    }
                                    echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                }

                                ?>
                            </select>
                        <?php } else { ?><b><?php echo $operatingStates; ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6  BenGbiTitle dbaNames_disp <?php echo loanForm::showField('dbaNames'); ?>">
                    <?php echo loanForm::label('dbaNames', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'dbaNames', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text"
                                   name="dbaNames"
                                   id="dbaNames"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo htmlentities(Strings::showField('dbaNames', 'fileHMLOEntityInfo')); ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'dbaNames', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                   orig-value="<?php echo Strings::showField('dbaNames', 'fileHMLOEntityInfo'); ?>">
                        <?php } else { ?>
                            <b><?php echo Strings::showField('dbaNames', 'fileHMLOEntityInfo'); ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6  BenGbiTitle priorBusName_disp <?php echo loanForm::showField('priorBusName'); ?>">
                    <?php echo loanForm::label('priorBusName', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'priorBusName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text"
                                   name="priorBusName"
                                   id="priorBusName" tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo htmlentities(Strings::showField('priorBusName', 'fileHMLOEntityInfo')); ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'priorBusName', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                   orig-value="<?php echo Strings::showField('priorBusName', 'fileHMLOEntityInfo'); ?>">
                        <?php } else { ?>
                            <b><?php echo Strings::showField('priorBusName', 'fileHMLOEntityInfo'); ?></b><?php } ?>
                    </div>
                </div>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenGbiTitle organizationalRef_disp <?php echo loanForm::showField('organizationalRef'); ?>">
                        <?php echo loanForm::label('organizationalRef', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'organizationalRef', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text" name="organizationalRef" id="organizationalRef"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlentities($organizationalRef); ?>"
                                       size="20"
                                       maxlength="30"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'organizationalRef', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo $organizationalRef; ?>">
                            <?php } else { ?><b><?php echo $organizationalRef; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenGbiTitle businessPhone_disp <?php echo loanForm::showField('businessPhone'); ?>">
                        <?php echo loanForm::label('businessPhone', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'businessPhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text" name="businessPhone" id="businessPhone"
                                       tabindex="<?php echo $tabIndex++; ?>" placeholder="(___) ___ - ____ Ext ____"
                                       value="<?php echo $businessPhone; ?>" size="20" maxlength="30"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'businessPhone', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo $businessPhone; ?>">
                            <?php } else { ?><b><?php echo $businessPhone; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>

                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenGbiTitle entityWebsite_disp <?php echo loanForm::showField('entityWebsite'); ?>">
                        <?php echo loanForm::label('entityWebsite', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityWebsite', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text"
                                       name="entityWebsite"
                                       id="entityWebsite"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlentities($entityWebsite); ?>"
                                       size="30"
                                       maxlength="75"
                                       autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'entityWebsite', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo $entityWebsite; ?>">
                            <?php } else { ?><b><?php echo $entityWebsite; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>


                <div class="clearfix"></div>

                <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="BenLpdTitle">
                    <label class="bg-secondary py-2 col-lg-12"><b><?php echo BaseHTML::getSubSectionHeading('BEN', 'locationPropDetailsSubSection'); ?></b></label>
                </div>


                <div class="form-group row col-md-6 BenLpdTitle startDateAtLocation_disp <?php echo loanForm::showField('startDateAtLocation'); ?>">
                    <?php echo loanForm::label('startDateAtLocation', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend startDateAtLocation">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm preVal date_mask <?php echo BaseHTML::fieldAccess(['fNm' => 'startDateAtLocation', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass "
                                       placeholder="MM/DD/YYYY" type="text" name="startDateAtLocation"
                                       id="startDateAtLocation" value="<?php echo $startDateAtLocation ?>"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'startDateAtLocation', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo $startDateAtLocation; ?>"/>
                            </div>
                        <?php } else { ?><b><?php echo $startDateAtLocation; ?></b><?php } ?>
                    </div>
                </div>
                <?php if ($hideThisField) { ?>
                    <script>
                        $(document).ready(function() {
                            $('#entityAddress').on('input', function() {
                                address_lookup.InitLegacy($(this));
                            });
                            $('#entityBillAddress').on('input', function() {
                                address_lookup.InitLegacy($(this));
                            });
                        });
                    </script>

                    <div class="form-group row col-md-6 BenLpdTitle entityAddress_disp <?php echo loanForm::showField('entityAddress'); ?>">
                        <?php echo loanForm::label('entityAddress', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityAddress', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text"
                                       name="entityAddress"
                                       id="entityAddress"
                                       data-address="entityAddress"
                                       data-unit="entityAddress2"
                                       data-city="entityCity"
                                       data-state="entityState"
                                       data-zip="entityZip"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlentities($entityAddress); ?>"
                                       size="40"
                                       maxlength="75"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityAddress', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo htmlentities($entityAddress); ?>">
                            <?php } else { ?><b><?php echo $entityAddress; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenLpdTitle entityAddress2_disp <?php echo loanForm::showField('entityAddress2'); ?>">
                        <?php echo loanForm::label('entityAddress2', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityAddress2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text" name="entityAddress2" id="entityAddress2"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlentities(Strings::showField('entityAddress2', 'fileHMLOEntityInfo')); ?>"
                                       maxlength="128"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityAddress2', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo htmlentities(Strings::showField('entityAddress2', 'fileHMLOEntityInfo')); ?>">
                            <?php } else { ?>
                                <b><?php echo htmlentities(Strings::showField('entityAddress2', 'fileHMLOEntityInfo')); ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenLpdTitle entityCity_disp <?php echo loanForm::showField('entityCity'); ?>">
                        <?php echo loanForm::label('entityCity', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityCity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text" name="entityCity" id="entityCity"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlentities($entityCity); ?>" size="20" maxlength="30"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityCity', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo htmlentities($entityCity); ?>">
                            <?php } else { ?><b><?php echo $entityCity; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenLpdTitle entityState_disp <?php echo loanForm::showField('entityState'); ?>">
                        <?php echo loanForm::label('entityState', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <select class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityState', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        name="entityState" id="entityState"
                                        tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityState', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                        orig-value="<?php echo $entityState; ?>">
                                    <option value=''> - Select -</option>
                                    <?php
                                    for ($j = 0; $j < count($stateArray); $j++) {
                                        $sOpt = '';
                                        $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $entityState);
                                        echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                    }
                                    ?>
                                </select>
                            <?php } else { ?><b><?php echo $entityState; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenLpdTitle entityZip_disp <?php echo loanForm::showField('entityZip'); ?>">
                        <?php echo loanForm::label('entityZip', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control zipCode  input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityZip', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text"
                                       name="entityZip"
                                       id="entityZip"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlentities($entityZip); ?>"
                                       size="10"
                                       maxlength="10"
                                       onblur="fieldValidation(this.id,this.name)"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityZip', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo htmlentities($entityZip); ?>">
                            <?php } else { ?><b><?php echo $entityZip; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <?php
                if (!$isClientProfile) {
                    ?>
                    <?php if ($hideThisField) { ?>
                        <div class="form-group row col-md-12 BenLpdTitle entityBillAddrChk_disp <?php echo loanForm::showField('entityBillAddrChk'); ?>">
                            <?php echo loanForm::label('entityBillAddrChk', 'col-md-5'); ?>
                            <div class="col-md-1">
                                <?php if ($allowToEdit) { ?>
                                    <span class="switch switch-icon <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBillAddrChk', 'sArr' => $secArr, 'opt' => 'M']); ?>">
                                    <label class="font-weight-bold">
                                        <input class="form-control" <?php if ($sameAsEntityAddr == '1') { ?> checked="checked" <?php } ?>
                                               id="entityBillAddrChk" type="checkbox"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               onchange="entToggleSwitch('entityBillAddrChk', 'sameAsEntityAddr', 'entityBillAddress' );">
                                        <input type="hidden"
                                               class="preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBillAddrChk', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="sameAsEntityAddr" id="sameAsEntityAddr"
                                               value="<?php echo $sameAsEntityAddr; ?>"
                                               orig-value="<?php echo $sameAsEntityAddr; ?>">
                                        <span></span>
                                    </label>
                                </span>
                                <?php } else {
                                    if ($sameAsEntityAddr == '1') {
                                        echo '<h5>Yes</h5>';
                                    } else {
                                        echo '<h5>No</h5>';
                                    }
                                } ?>
                            </div>
                        </div>
                    <?php } ?>
                    <?php if ($hideThisField) { ?>
                        <div class="entityBillAddress row col-md-12 <?php echo BaseHTML::parentFieldAccess(['fNm' => 'entityBillAddrChk', 'sArr' => $secArr, 'pv' => $entityBillAddrChk, 'av' => '0']); ?>"
                             style="<?php echo $sameAsEntityDisOption; ?>">
                            <?php if ($hideThisField) { ?>
                                <div class="form-group row col-md-6 BenLpdTitle entityBillAddress_disp <?php echo loanForm::showField('entityBillAddress'); ?>">
                                    <?php echo loanForm::label('entityBillAddress', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBillAddress', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="text"
                                                   name="entityBillAddress"
                                                   id="entityBillAddress"
                                                   data-address="entityBillAddress"
                                                   data-city="entityBillCity"
                                                   data-state="entityBillState"
                                                   data-zip="entityBillZip"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo htmlentities($entityBillAddress); ?>"
                                                   size="40" maxlength="75"
                                                   autocomplete="off"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBillAddress', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                   orig-value="<?php echo htmlentities($entityBillAddress); ?>">
                                        <?php } else { ?><b><?php echo $entityBillAddress; ?></b><?php } ?>
                                    </div>
                                </div>
                            <?php } ?>

                            <div class="form-group row col-md-6 BenLpdTitle entityBillCity_disp <?php echo loanForm::showField('entityBillCity'); ?>">
                                <?php echo loanForm::label('entityBillCity', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBillCity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               type="text"
                                               name="entityBillCity"
                                               id="entityBillCity"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               value="<?php echo htmlentities($entityBillCity); ?>"
                                               size="20"
                                               maxlength="30"
                                               autocomplete="off"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBillCity', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                               orig-value="<?php echo htmlentities($entityBillCity); ?>">
                                    <?php } else { ?><b><?php echo $entityBillCity; ?></b><?php } ?>
                                </div>
                            </div>
                            <div class="form-group row col-md-6 BenLpdTitle entityBillState_disp <?php echo loanForm::showField('entityBillState'); ?>">
                                <?php echo loanForm::label('entityBillState', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <select name="entityBillState" id="entityBillState"
                                                class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBillState', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBillState', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                orig-value="<?php echo $entityBillState; ?>"
                                                tabindex="<?php echo $tabIndex++; ?>">
                                            <option value="">-Select-</option>
                                            <?php
                                            for ($a = 0; $a < count($stateArray); $a++) {
                                                ?>
                                                <option value="<?php echo trim($stateArray[$a]['stateCode']); ?>" <?php if (trim($stateArray[$a]['stateCode']) == $entityBillState) echo 'selected=selected'; ?> ><?php echo trim($stateArray[$a]['stateName']); ?></option>
                                            <?php } ?>
                                        </select>
                                    <?php } else { ?><b><?php echo $entityBillState; ?></b><?php } ?>
                                </div>
                            </div>
                            <div class="form-group row col-md-6 BenLpdTitle entityBillZip_disp  <?php echo loanForm::showField('entityBillZip'); ?>">
                                <?php echo loanForm::label('entityBillZip', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input class="form-control zipCode preVal  input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBillZip', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               type="text" name="entityBillZip" id="entityBillZip"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               value="<?php echo htmlentities($entityBillZip); ?>"
                                               size="10"
                                               maxlength="10"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBillZip', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                               orig-value="<?php echo htmlentities($entityBillZip); ?>">
                                    <?php } else { ?><b><?php echo $entityBillZip; ?></b><?php } ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <?php
                } // isClientProfile end
                ?>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenLpdTitle entityLocation_disp <?php echo loanForm::showField('entityLocation'); ?>">
                        <?php echo loanForm::label('entityLocation', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="entityLocationUrban">

                                        <input type="radio"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'entityLocation', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="entityLocation" id="entityLocationUrban"
                                               value="Urban" <?php echo Strings::isChecked('Urban', $entityLocation); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'entityLocation', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                        Urban
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="entityLocationRural">
                                        <input type="radio"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'entityLocation', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="entityLocation" id="entityLocationRural"
                                               value="Rural" <?php echo Strings::isChecked('Rural', $entityLocation); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'entityLocation', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                        Rural
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="entityLocationRuralSuburban">
                                        <input type="radio"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'entityLocation', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="entityLocation" id="entityLocationRuralSuburban"
                                               value="Suburban" <?php echo Strings::isChecked('Suburban', $entityLocation); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'entityLocation', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                        Suburban
                                    </label></div>

                            <?php } else { ?><b><?php echo $entityLocation; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div class="form-group row col-md-6 BenLpdTitle entityPropertyOwnerShip_disp <?php echo loanForm::showField('entityPropertyOwnerShip'); ?>">
                    <?php echo loanForm::label('entityPropertyOwnerShip', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'entityPropertyOwnerShip', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="entityPropertyOwnerShip" id="entityPropertyOwnerShip"
                                    tabindex="<?php echo $tabIndexNo++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityPropertyOwnerShip', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                <option value=""> - Select -</option>
                                <?php
                                for ($i = 0; $i < count($glPropTypeArray); $i++) {
                                    $sOpt = '';
                                    $glPropType = '';
                                    $glPropType = trim($glPropTypeArray[$i]);
                                    $sOpt = Arrays::isSelected($glPropType, $entityPropertyOwnerShip);
                                    echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?><h5><?php echo $entityPropertyOwnerShip; ?></h5><?php } ?>
                    </div>
                </div>

                <div class="OwnershipChildFieldsDiv <?php echo BaseHTML::parentFieldAccess(['fNm' => 'entityPropertyOwnerShip', 'sArr' => $secArr, 'pv' => $entityPropertyOwnerShip, 'av' => 'Own']); ?>"
                     id="OwnershipChildFieldsDiv">
                    <div class="form-group row col-md-6 BenLpdTitle showHideOwnershipChildFields valueOfProperty_disp <?php echo loanForm::showField('valueOfProperty'); ?>">
                        <?php echo loanForm::label('valueOfProperty', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text"
                                           placeholder="0.00"
                                           class="form-control input-sm  preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'valueOfProperty', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="valueOfProperty" id="valueOfProperty"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo Strings::CurrencyForm($valueOfProperty); ?>"
                                           autocomplete="off"
                                           size="10"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'valueOfProperty', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                           onblur="currencyConverter(this, this.value);"/>
                                </div>
                            <?php } else {
                                echo $valueOfProperty;
                            } ?>
                        </div>
                    </div>
                    <div class="form-group row col-md-6 BenLpdTitle showHideOwnershipChildFields totalDebtOnProperty_disp <?php echo loanForm::showField('totalDebtOnProperty'); ?>">
                        <?php echo loanForm::label('totalDebtOnProperty', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text"
                                           placeholder="0.00"
                                           class="form-control input-sm  preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'totalDebtOnProperty', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="totalDebtOnProperty" id="totalDebtOnProperty"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo Strings::CurrencyForm($totalDebtOnProperty); ?>"
                                           autocomplete="off"
                                           size="10"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'totalDebtOnProperty', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                           onblur="currencyConverter(this, this.value);"/>
                                </div>
                            <?php } else {
                                echo $totalDebtOnProperty;
                            } ?>
                        </div>
                    </div>
                    <div class="form-group row col-md-6 BenLpdTitle showHideOwnershipChildFields nameOfLenders_disp <?php echo loanForm::showField('nameOfLenders'); ?>">
                        <?php echo loanForm::label('nameOfLenders', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <textarea name="nameOfLenders" id="nameOfLenders"
                                          class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'nameOfLenders', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                          tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'nameOfLenders', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo htmlentities($nameOfLenders); ?></textarea>
                            <?php } else {
                                echo $nameOfLenders;
                            } ?>
                        </div>
                    </div>
                </div>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row col-md-6 BenLpdTitle landlordMortagageContactName_disp <?php echo loanForm::showField('landlordMortagageContactName'); ?>">
                        <?php echo loanForm::label('landlordMortagageContactName', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'landlordMortagageContactName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text" name="landlordMortagageContactName"
                                       id="landlordMortagageContactName"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlentities($landlordMortagageContactName); ?>" size="20"
                                       maxlength="100"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'landlordMortagageContactName', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo htmlentities($landlordMortagageContactName); ?>">
                            <?php } else { ?><b><?php echo $landlordMortagageContactName; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <?php if ($hideThisField) { ?>
                    <div class="clearfix"></div>
                    <div class="form-group row col-md-6 BenLpdTitle landlordMortagagePhone_disp <?php echo loanForm::showField('landlordMortagagePhone'); ?>">
                        <?php echo loanForm::label('landlordMortagagePhone', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal mask_cellnew <?php echo BaseHTML::fieldAccess(['fNm' => 'landlordMortagagePhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text" name="landlordMortagagePhone" id="landlordMortagagePhone"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo $landlordMortagagePhone; ?>"
                                       size="20" maxlength="20" placeholder="(___) ___ - ____"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'landlordMortagagePhone', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo $landlordMortagagePhone; ?>">
                            <?php } else { ?><b><?php echo $landlordMortagagePhone; ?></b><?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div class="form-group row col-md-6 BenLpdTitle rentMortagagePayment_disp <?php echo loanForm::showField('rentMortagagePayment'); ?>">
                    <?php echo loanForm::label('rentMortagagePayment', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm preVal decimal <?php echo BaseHTML::fieldAccess(['fNm' => 'rentMortagagePayment', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text" name="rentMortagagePayment" id="rentMortagagePayment"
                                   placeholder="0.00"
                                   onkeyup='return restrictAlphabets(this)'
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($rentMortagagePayment); ?>" size="20" maxlength="11"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'rentMortagagePayment', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                   orig-value="<?php echo Currency::formatDollarAmountWithDecimal($rentMortagagePayment); ?>">
                        <?php } else { ?><b><?php echo Currency::formatDollarAmountWithDecimal($rentMortagagePayment); ?></b><?php } ?>
                    </div>
                </div>
                <div class="clearfix"></div>

                <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="BenBdTitle">
                    <label class="bg-secondary  py-2  col-lg-12"><b><?php echo BaseHTML::getSubSectionHeading('BEN', 'businessDetailsSubSection'); ?></b></label>
                </div>

                <div class="form-group row col-md-6 BenBdTitle businessType_disp <?php echo loanForm::showField('businessType'); ?>">
                    <?php echo loanForm::label('businessType', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select name="businessType"
                                    class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'businessType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    id="businessType"
                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'businessType', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                    orig-value="<?php echo $businessType; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                for ($i = 0; $i < count(glBusinessType::$glBusinessType); $i++) {
                                    $sOpt = '';
                                    $sOpt = Arrays::isSelected(glBusinessType::$glBusinessType[$i], $businessType);
                                    echo "<option value=\"" . glBusinessType::$glBusinessType[$i] . "\" " . $sOpt . '>' . glBusinessType::$glBusinessType[$i] . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?><b><?php echo $businessType; ?></b><?php } ?>
                    </div>
                </div>

                <div class="form-group row col-md-6 BenBdTitle noOfEmployees_disp <?php echo loanForm::showField('noOfEmployees'); ?>">
                    <?php echo loanForm::label('noOfEmployees', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfEmployees', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="number"
                                   name="noOfEmployees"
                                   id="noOfEmployees"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo $noOfEmployees; ?>" size="10"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfEmployees', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                   orig-value="<?php echo $noOfEmployees; ?>">
                        <?php } else {
                            echo $noOfEmployees;
                        } ?>
                    </div>
                </div>

                <div class="form-group row col-md-6 BenBdTitle noOfEmployeesAfterLoan_disp <?php echo loanForm::showField('noOfEmployeesAfterLoan'); ?>">
                    <?php echo loanForm::label('noOfEmployeesAfterLoan', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfEmployeesAfterLoan', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="number"
                                   name="noOfEmployeesAfterLoan"
                                   id="noOfEmployeesAfterLoan"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo $noOfEmployeesAfterLoan; ?>" size="10"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfEmployeesAfterLoan', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                   orig-value="<?php echo $noOfEmployeesAfterLoan; ?>">
                        <?php } else {
                            echo $noOfEmployeesAfterLoan;
                        } ?>
                    </div>
                </div>

                <div class="form-group row col-md-6 BenBdTitle businessCategory_disp <?php echo loanForm::showField('businessCategory'); ?>">
                    <?php echo loanForm::label('businessCategory', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select name="businessCategory"
                                    class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'businessCategory', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    id="businessCategory"
                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'businessCategory', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                    orig-value="<?php echo $businessCategory; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                for ($i = 0; $i < count($glbusinessCategoryArray); $i++) {
                                    $sOpt = '';
                                    $glbusinessCategory = '';
                                    $glbusinessCategory = trim($glbusinessCategoryArray[$i]);
                                    $sOpt = Arrays::isSelected($glbusinessCategory, $businessCategory);
                                    echo "<option value=\"" . $glbusinessCategory . "\" " . $sOpt . '>' . $glbusinessCategory . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?><b><?php echo $businessCategory; ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBdTitle productTypeOrServiceSold_disp <?php echo loanForm::showField('productTypeOrServiceSold'); ?>">
                    <?php echo loanForm::label('productTypeOrServiceSold', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <textarea type="text" name="productTypeOrServiceSold" id="productTypeOrServiceSold"
                                      tabindex="<?php echo $tabIndex++; ?>"
                                      class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'productTypeOrServiceSold', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'productTypeOrServiceSold', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo $productTypeOrServiceSold; ?></textarea>
                        <?php } else { ?><b><?php echo $productTypeOrServiceSold; ?></b><?php } ?>
                    </div>
                </div>

                <div class="form-group row col-md-6 BenBdTitle naicsCode_disp <?php echo loanForm::showField('naicsCode'); ?> ">
                    <?php echo loanForm::label('naicsCode', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="number" name="naicsCode" id="naicsCode"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'naicsCode', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $naicsCode; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'naicsCode', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                            <a href="https://www.naics.com/search/" target="_blank">NAICS Code look up</a>
                        <?php } else { ?><b><?php echo $naicsCode; ?></b><?php } ?>
                    </div>
                </div>
                <div class="clearfix"></div>

                <div class="form-group row col-md-6 BenBdTitle entityBusinessSell_disp <?php echo loanForm::showField('entityBusinessSell'); ?>">
                    <?php echo loanForm::label('entityBusinessSell', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <!-- for checkboxes same id added for mandatory validation msg -->
                            <div class="checkbox-inline">
                                <label class="checkbox">
                                    <input type="checkbox"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'entityBusinessSell', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="entityService" id="entityBusinessSellService"
                                           value="Service" <?php echo Strings::isChecked('Service', $entityService); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBusinessSell', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                    Service
                                </label>
                                <label class="checkbox">
                                    <input type="checkbox"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'entityBusinessSell', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="entityProduct" id="entityBusinessSellService"
                                           value="Product" <?php echo Strings::isChecked('Product', $entityProduct); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBusinessSell', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                    Product
                                </label>
                            </div>
                        <?php } else { ?><b><?php echo $entityService . ' ' . $entityProduct; ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBdTitle entityBusinessType_disp <?php echo loanForm::showField('entityBusinessType'); ?>">
                    <?php echo loanForm::label('entityBusinessType', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="checkbox-inline">
                                <label class="checkbox">
                                    <input type="checkbox" name="entityB2B"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'entityBusinessType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="entityBusinessType"
                                           value="B2B" <?php echo Strings::isChecked('B2B', $entityB2B); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBusinessType', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                    B2B
                                </label>
                                <label class="checkbox">
                                    <input type="checkbox" name="entityB2C"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'entityBusinessType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="entityBusinessType"
                                           value="B2C" <?php echo Strings::isChecked('B2C', $entityB2C); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'entityBusinessType', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                    B2C
                                </label>
                            </div>
                        <?php } else { ?><b><?php echo $entityB2B . ' ' . $entityB2C; ?></b><?php } ?>
                    </div>
                </div>
                <div class="clearfix"></div>
                <div class="form-group row col-md-6 BenBdTitle benBusinessHomeBased_disp <?php echo loanForm::showField('benBusinessHomeBased'); ?>">
                    <?php echo loanForm::label('benBusinessHomeBased', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="benBusinessHomeBasedYes">
                                    <input type="radio" name="benBusinessHomeBased" id="benBusinessHomeBasedYes"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessHomeBased', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessHomeBased', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('Yes', $benBusinessHomeBased); ?>
                                           value="Yes"/><span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="benBusinessHomeBasedNo">
                                    <input type="radio" name="benBusinessHomeBased" id="benBusinessHomeBasedNo"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessHomeBased', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessHomeBased', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('No', $benBusinessHomeBased); ?>
                                           value="No"/><span></span>No
                                </label>
                            </div>
                        <?php } else {
                            echo $benBusinessHomeBased;
                        } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBdTitle benCreditCardPayments_disp <?php echo loanForm::showField('benCreditCardPayments'); ?>">
                    <?php echo loanForm::label('benCreditCardPayments', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="benCreditCardPaymentsYes">
                                    <input type="radio" name="benCreditCardPayments" id="benCreditCardPaymentsYes"
                                           class="benCreditCardPayments <?php echo BaseHTML::fieldAccess(['fNm' => 'benCreditCardPayments', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'benCreditCardPayments', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('Yes', $benCreditCardPayments); ?>
                                           value="Yes"/><span></span> Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="benCreditCardPaymentsNo">
                                    <input type="radio" name="benCreditCardPayments" id="benCreditCardPaymentsNo"
                                           class="benCreditCardPayments <?php echo BaseHTML::fieldAccess(['fNm' => 'benCreditCardPayments', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'benCreditCardPayments', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('No', $benCreditCardPayments); ?>
                                           value="No"/><span></span> No
                                </label>
                            </div>
                        <?php } else {
                            echo $benCreditCardPayments;
                        } ?>
                    </div>
                </div>

                <div id="creditCardFieldsDiv"
                     class="form-group row col-md-12  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'benCreditCardPayments', 'sArr' => $secArr, 'pv' => $benCreditCardPayments, 'av' => 'Yes']); ?>">
                    <div class="form-group row col-md-6 terminalOrMakeModel_disp <?php echo loanForm::showField('terminalOrMakeModel'); ?>">
                        <?php echo loanForm::label('terminalOrMakeModel', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'terminalOrMakeModel', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text" name="terminalOrMakeModel" id="terminalOrMakeModel"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlentities($terminalOrMakeModel); ?>"
                                       size="20" maxlength="30"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'terminalOrMakeModel', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo $terminalOrMakeModel; ?>">
                            <?php } else { ?><b><?php echo $terminalOrMakeModel; ?></b><?php } ?>
                        </div>
                    </div>
                    <div class="form-group row col-md-6 benCardProcessorBank_disp <?php echo loanForm::showField('benCardProcessorBank'); ?>">
                        <?php echo loanForm::label('benCardProcessorBank', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'benCardProcessorBank', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text" name="benCardProcessorBank" id="benCardProcessorBank"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlentities($benCardProcessorBank); ?>" size="20"
                                       maxlength="30"
                                       autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'benCardProcessorBank', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                            <?php } else { ?><b><?php echo $benCardProcessorBank; ?></b><?php } ?>
                        </div>
                    </div>
                    <?php if ($hideThisField) { ?>
                        <div class="form-group row col-md-6 merchantProcessingBankName_disp <?php echo loanForm::showField('merchantProcessingBankName'); ?>">
                            <?php echo loanForm::label('merchantProcessingBankName', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'merchantProcessingBankName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           type="text" name="merchantProcessingBankName" id="merchantProcessingBankName"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo htmlentities($merchantProcessingBankName); ?>" size="20"
                                           maxlength="30"
                                           autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'merchantProcessingBankName', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                                <?php } else { ?><b><?php echo $merchantProcessingBankName; ?></b><?php } ?>
                            </div>
                        </div>
                    <?php } ?>
                </div>


                <div class="form-group row col-md-6 BenBdTitle benChargeSalesTax_disp <?php echo loanForm::showField('benChargeSalesTax'); ?>">
                    <?php echo loanForm::label('benChargeSalesTax', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="benChargeSalesTaxYes">
                                    <input class="<?php echo BaseHTML::fieldAccess(['fNm' => 'benChargeSalesTax', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           type="radio" name="benChargeSalesTax" id="benChargeSalesTaxYes"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="Yes" <?php echo BaseHTML::fieldAccess(['fNm' => 'benChargeSalesTax', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('Yes', $benChargeSalesTax); ?>/><span></span>
                                    Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="benChargeSalesTaxNo">
                                    <input class="<?php echo BaseHTML::fieldAccess(['fNm' => 'benChargeSalesTax', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           type="radio" name="benChargeSalesTax" id="benChargeSalesTaxNo"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="No" <?php echo BaseHTML::fieldAccess(['fNm' => 'benChargeSalesTax', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('No', $benChargeSalesTax); ?>/><span></span>
                                    No
                                </label>
                            </div>

                        <?php } else { ?><b><?php echo $benChargeSalesTax; ?></b><?php } ?>
                    </div>
                </div>
                <div class="clearfix"></div>
                <div class="form-group row col-md-6 BenBdTitle benEmployeesPaid_disp <?php echo loanForm::showField('benEmployeesPaid'); ?>">
                    <?php echo loanForm::label('benEmployeesPaid', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select name="benEmployeesPaid" id="benEmployeesPaid"
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'benEmployeesPaid', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'benEmployeesPaid', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <option value="">- Select -</option>
                                <?php foreach (glBenEmployeesPaid::$glBenEmployeesPaid as $key1 => $value) { ?>
                                    <option value="<?php echo $key1; ?>" <?php if ($benEmployeesPaid == $key1) echo 'selected = selected'; ?> ><?php echo $value; ?></option>
                                <?php } ?>
                            </select>
                        <?php } else { ?><b><?php echo $benEmployeesPaid; ?></b><?php } ?>
                    </div>
                </div>


                <div class="form-group row col-md-6 BenBdTitle benBusinessLocation_disp <?php echo loanForm::showField('benBusinessLocation'); ?> ">
                    <?php echo loanForm::label('benBusinessLocation', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="benBusinessLocationYes">
                                    <input type="radio"
                                           class="benBusinessLocation <?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessLocation', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="benBusinessLocation" id="benBusinessLocationYes"
                                           value="Yes" <?php echo Strings::isChecked('Yes', $benBusinessLocation); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessLocation', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                    Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="benBusinessLocationNo">
                                    <input type="radio"
                                           class="benBusinessLocation <?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessLocation', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="benBusinessLocation" id="benBusinessLocationNo"
                                           value="No" <?php echo Strings::isChecked('No', $benBusinessLocation); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessLocation', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                    No
                                </label>
                            </div>
                        <?php } else { ?><b><?php echo $benBusinessLocation; ?></b><?php } ?>
                    </div>
                </div>


                <div id="BenBusinessLocationDiv"
                     class="row col-md-12 <?php echo BaseHTML::parentFieldAccess(['fNm' => 'benBusinessLocation', 'sArr' => $secArr, 'pv' => $benBusinessLocation, 'av' => 'Yes']); ?>">
                    <div class="form-group row col-md-6 benHowManyLocation_disp <?php echo loanForm::showField('benHowManyLocation'); ?>">
                        <?php echo loanForm::label('benHowManyLocation', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'benHowManyLocation', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="number"
                                       name="benHowManyLocation"
                                       id="benHowManyLocation"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo $benHowManyLocation; ?>" size="20" maxlength="30"
                                       autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'benHowManyLocation', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                            <?php } else { ?><b><?php echo $benHowManyLocation; ?></b><?php } ?>
                        </div>
                    </div>
                    <div class="form-group row col-md-6 benOtherLocation_disp <?php echo loanForm::showField('benOtherLocation'); ?>">
                        <?php echo loanForm::label('benOtherLocation', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <textarea name="benOtherLocation" id="benOtherLocation"
                                          class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'benOtherLocation', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                          tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'benOtherLocation', 'sArr' => $secArr, 'opt' => 'I']); ?> ><?php echo $benOtherLocation; ?></textarea>
                            <?php } else { ?><b><?php echo $benOtherLocation; ?></b><?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group row col-md-6 BenBdTitle benBusinessFranchise_disp <?php echo loanForm::showField('benBusinessFranchise'); ?> ">
                    <?php echo loanForm::label('benBusinessFranchise', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="benBusinessFranchiseYes">
                                    <input type="radio"
                                           class="benBusinessFranchise <?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessFranchise', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="benBusinessFranchise" id="benBusinessFranchiseYes"
                                           value="Yes" <?php echo Strings::isChecked('Yes', $benBusinessFranchise); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessFranchise', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                    Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="benBusinessFranchiseNo">
                                    <input type="radio"
                                           class="benBusinessFranchise <?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessFranchise', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="benBusinessFranchise" id="benBusinessFranchiseNo"
                                           value="No" <?php echo Strings::isChecked('No', $benBusinessFranchise); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'benBusinessFranchise', 'sArr' => $secArr, 'opt' => 'I']); ?>/><span></span>
                                    No
                                </label></div>
                        <?php } else { ?><b><?php echo $benBusinessFranchise; ?></b><?php } ?>
                    </div>
                </div>
                <?php if ($hideThisField) { ?>
                    <div id="BenBusinessFranchiseDiv"
                         class="form-group row col-md-12 <?php echo BaseHTML::parentFieldAccess(['fNm' => 'benBusinessFranchise', 'sArr' => $secArr, 'pv' => $benBusinessFranchise, 'av' => 'Yes']); ?>">
                        <div class="form-group row col-md-6 benNameOfFranchise_disp <?php echo loanForm::showField('benNameOfFranchise'); ?>">
                            <?php echo loanForm::label('benNameOfFranchise', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'benNameOfFranchise', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           type="text" name="benNameOfFranchise" id="benNameOfFranchise"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo htmlentities($benNameOfFranchise); ?>" size="20"
                                           maxlength="30"
                                           autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'benNameOfFranchise', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                                <?php } else { ?><b><?php echo $benNameOfFranchise; ?></b><?php } ?>
                            </div>
                        </div>
                        <div class="form-group row col-md-6 benPointOfContact_disp <?php echo loanForm::showField('benPointOfContact'); ?>">
                            <?php echo loanForm::label('benPointOfContact', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'benPointOfContact', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           type="text" name="benPointOfContact" id="benPointOfContact"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo htmlentities($benPointOfContact); ?>" size="20"
                                           maxlength="30"
                                           autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'benPointOfContact', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                                <?php } else { ?><b><?php echo $benPointOfContact; ?></b><?php } ?>
                            </div>
                        </div>
                        <div class="form-group row col-md-6 benPointOfContactPhone_disp <?php echo loanForm::showField('benPointOfContactPhone'); ?>">
                            <?php echo loanForm::label('benPointOfContactPhone', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input class="form-control input-sm preVal mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'benPointOfContactPhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           type="text" name="benPointOfContactPhone" id="benPointOfContactPhone"
                                           tabindex="<?php echo $tabIndex++; ?>" placeholder="(___) ___ - ____ Ext ____"
                                           value="<?php echo $benPointOfContactPhone; ?>" autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'benPointOfContact', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                                <?php } else { ?><b><?php echo $benPointOfContactPhone; ?></b><?php } ?>
                            </div>
                        </div>
                        <div class="form-group row col-md-6 benPointOfContactEmail_disp <?php echo loanForm::showField('benPointOfContactEmail'); ?>">
                            <?php echo loanForm::label('benPointOfContactEmail', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'benPointOfContactEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           type="email" name="benPointOfContactEmail" id="benPointOfContactEmail"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo $benPointOfContactEmail; ?>" autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'benPointOfContactEmail', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                                <?php } else { ?><b><?php echo $benPointOfContactEmail; ?></b><?php } ?>
                            </div>
                        </div>
                        <div class="form-group row col-md-6 benWebsiteForFranchise_disp <?php echo loanForm::showField('benWebsiteForFranchise'); ?>">
                            <?php echo loanForm::label('benWebsiteForFranchise', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'benWebsiteForFranchise', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           type="text" name="benWebsiteForFranchise" id="benWebsiteForFranchise"
                                           placeholder="https://"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo htmlentities($benWebsiteForFranchise); ?>" size="20"
                                           maxlength="30"
                                           autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'benPointOfContact', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                                <?php } else { ?><b><?php echo $benWebsiteForFranchise; ?></b><?php } ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <div class="clearfix"></div>
                <div class="form-group row col-md-6 BenBdTitle isBusinessSeasonal_disp <?php echo loanForm::showField('isBusinessSeasonal'); ?> ">
                    <?php echo loanForm::label('isBusinessSeasonal', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="isBusinessSeasonalYes">
                                    <input type="radio" name="isBusinessSeasonal" id="isBusinessSeasonalYes"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isBusinessSeasonal', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           value="Yes"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'isBusinessSeasonal', 'sArr' => $secArr, 'opt' => 'I']); ?> <?php echo Strings::isChecked('Yes', $isBusinessSeasonal); ?>
                                           onclick="hideAndShowSection(this.value, 'Yes', 'isBusinessSeasonalPeakMonth_disp')">
                                    <span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="isBusinessSeasonalNo">
                                    <input type="radio" name="isBusinessSeasonal" id="isBusinessSeasonalNo"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isBusinessSeasonal', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           value="No"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'isBusinessSeasonal', 'sArr' => $secArr, 'opt' => 'I']); ?><?php echo Strings::isChecked('No', $isBusinessSeasonal); ?>
                                           onclick="hideAndShowSection(this.value, 'Yes', 'isBusinessSeasonalPeakMonth_disp')">
                                    <span></span>No
                                </label>
                            </div>
                        <?php } else { ?><b><?php echo $isBusinessSeasonal; ?></b><?php } ?>
                    </div>
                </div>

                <div class="form-group row col-md-6 BenBdTitle isBusinessSeasonalPeakMonth_disp <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isBusinessSeasonal', 'sArr' => $secArr, 'pv' => $isBusinessSeasonal, 'av' => 'Yes']); ?>">
                    <?php echo loanForm::label('isBusinessSeasonalPeakMonth', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="isBusinessSeasonalPeakMonth"
                                   id="isBusinessSeasonalPeakMonth"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'isBusinessSeasonalPeakMonth', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'isBusinessSeasonalPeakMonth', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                   value="<?php echo htmlentities($isBusinessSeasonalPeakMonth); ?>">
                        <?php } else { ?><b><?php echo $isBusinessSeasonalPeakMonth; ?></b><?php } ?>
                    </div>
                </div>
                <!-- Business Description field to be at last if any new fields are added in this subsection -->

                <div class="form-group row col-md-6 BenBdTitle businessDescription_disp <?php echo loanForm::showField('businessDescription'); ?>">
                    <?php echo loanForm::label('businessDescription', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <textarea type="text" name="businessDescription" id="businessDescription"
                                      tabindex="<?php echo $tabIndex++; ?>"
                                      class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'businessDescription', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'businessDescription', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo $businessDescription; ?></textarea>
                        <?php } else { ?><b><?php echo $businessDescription; ?></b><?php } ?>
                    </div>
                </div>
                <div class="clearfix"></div>

                <?php if (!$isClientProfile && $hideThisField) { ?>
                    <!-- business Reference -->
                    <div class="form-group row col-md-12 BenBdTitle businessReference_disp <?php echo loanForm::showField('businessReference'); ?>">
                        <?php echo loanForm::label('businessReference', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="businessReferenceYes">
                                        <input type="radio" name="businessReference" id="businessReferenceYes"
                                               class="businessReference <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReference', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReference', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('1', $businessReference); ?>
                                               value="1"/><span></span>Yes
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="businessReferenceNo">
                                        <input type="radio" name="businessReference" id="businessReferenceNo"
                                               class="businessReference <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReference', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReference', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo Strings::isChecked('0', $businessReference); ?>
                                               value="0"/><span></span>No
                                    </label>
                                </div>
                            <?php } else {
                                if ($businessReference == '0') {
                                    echo 'No';
                                } else if ($businessReference == '1') {
                                    echo 'Yes';
                                } else {
                                    echo '';
                                }
                            } ?>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                    <?php //if (count($fileHMLOEntityRefInfoArray) > 0) {
                    if (count($fileHMLOEntityRefInfoArray ?? []) == 0) {
                        $fileHMLOEntityRefInfoArray = [''];
                    }
                    $refTotalCount = count($fileHMLOEntityRefInfoArray);
                    $refCount = 1;
                    foreach ($fileHMLOEntityRefInfoArray as $eachRefInfo) {
                        $businessReferenceName = '';
                        if (isset($eachRefInfo['businessReferenceName'])) {
                            $businessReferenceName = $eachRefInfo['businessReferenceName'];
                        }
                        $businessReferencePhone = '';
                        if (isset($eachRefInfo['businessReferencePhone'])) {
                            $businessReferencePhone = $eachRefInfo['businessReferencePhone'];
                        }
                        $businessReferenceCompany = '';
                        if (isset($eachRefInfo['businessReferenceCompany'])) {
                            $businessReferenceCompany = $eachRefInfo['businessReferenceCompany'];
                        }
                        $businessReferenceFax = '';
                        if (isset($eachRefInfo['businessReferenceFax'])) {
                            $businessReferenceFax = $eachRefInfo['businessReferenceFax'];
                        }
                        $businessReferenceEmail = '';
                        if (isset($eachRefInfo['businessReferenceEmail'])) {
                            $businessReferenceEmail = $eachRefInfo['businessReferenceEmail'];
                        }
                        ?>
                        <div class="businessReferenceInfo cloneBusinessReferenceInfo row <?php echo BaseHTML::parentFieldAccess(['fNm' => 'businessReference', 'sArr' => $secArr, 'pv' => $businessReference, 'av' => '1']); ?>"
                             style="clear: both;<?php echo $refCount % 2;
                             if ($refCount % 2 == 0) {
                                 echo 'background-color:red;';
                             } ?>" id="businessReferenceInfo_<?php echo $refCount; ?>">
                            <div class=" col-md-6 businessReferenceName_disp <?php echo loanForm::showField('businessReferenceName'); ?>">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-5"
                                           for="businessReferenceName"><span
                                            class="businesRefNumber"><?php echo $refCount; ?></span>) <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferenceName', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                    </label>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferenceName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="text" name="businessReferenceName[]"
                                                   id="businessReferenceName_<?php echo $refCount; ?>"
                                                   tabindex="<?php //echo $tabIndex++; ?>"
                                                   value="<?php echo htmlentities($businessReferenceName); ?>"
                                                   size="20" maxlength="30"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferenceName', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                   orig-value="<?php echo $businessReferenceName; ?>">
                                        <?php } else { ?><b><?php echo $businessReferenceName; ?></b><?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-6 businessReferencePhone_disp <?php echo loanForm::showField('businessReferencePhone'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('businessReferencePhone', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control mask_phone input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferencePhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="text" name="businessReferencePhone[]"
                                                   id="businessReferencePhone_<?php echo $refCount; ?>"
                                                   tabindex="<?php //echo $tabIndex++; ?>"
                                                   value="<?php echo $businessReferencePhone; ?>" size="20"
                                                   maxlength="30" autocomplete="off"
                                                   placeholder="(___) ___ - ____ Ext ____"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferencePhone', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                                        <?php } else { ?><b><?php echo $businessReferencePhone; ?></b><?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-6 businessReferenceCompany_disp <?php echo loanForm::showField('businessReferenceCompany'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('businessReferenceCompany', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferenceCompany', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="text" name="businessReferenceCompany[]"
                                                   id="businessReferenceCompany_<?php echo $refCount; ?>"
                                                   tabindex="<?php //echo $tabIndex++; ?>"
                                                   value="<?php echo htmlentities($businessReferenceCompany); ?>"
                                                   size="20"
                                                   maxlength="30"
                                                   autocomplete="off"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferenceCompany', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                                        <?php } else { ?><b><?php echo $businessReferenceCompany; ?></b><?php } ?>
                                    </div>
                                </div>
                            </div>


                            <div class=" col-md-6 businessReferenceFax_disp <?php echo loanForm::showField('businessReferenceFax'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('businessReferenceFax', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control mask_cellnew input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferenceFax', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="text" name="businessReferenceFax[]"
                                                   id="businessReferenceFax_<?php echo $refCount; ?>"
                                                   tabindex="<?php //echo $tabIndex++; ?>"
                                                   value="<?php echo $businessReferenceFax; ?>" size="20" maxlength="30"
                                                   autocomplete="off" placeholder="(___) ___ - ____"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferenceFax', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                                        <?php } else { ?><b><?php echo $businessReferenceFax; ?></b><?php } ?>
                                    </div>
                                </div>
                            </div>


                            <div class=" col-md-6 businessReferenceEmail_disp <?php echo loanForm::showField('businessReferenceEmail'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('businessReferenceEmail', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferenceEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="email" name="businessReferenceEmail[]"
                                                   id="businessReferenceEmail_<?php echo $refCount; ?>"
                                                   tabindex="<?php //echo $tabIndex++; ?>"
                                                   value="<?php echo $businessReferenceEmail; ?>" size="20"
                                                   maxlength="30"
                                                   autocomplete="off"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'businessReferenceEmail', 'sArr' => $secArr, 'opt' => 'I']); ?>">
                                        <?php } else { ?><b><?php echo $businessReferenceEmail; ?></b><?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-3">&nbsp;</div>
                            <div class=" col-md-3 deleteRow" id="deleteRow_<?php echo $refCount; ?>">
                                <?php //echo $refTotalCount . " " . $refCount; ?>
                                <i class="fa fa-minus-circle text-danger deleteBusinessRef deleteBusinessRef_<?php echo $refCount; ?> "
                                   data-hide-class="cloneBusinessRef_<?php echo $refCount; ?>"
                                   data-delete-row="businessReferenceInfo_<?php echo $refCount; ?>"
                                    <?php if (($refTotalCount - $refCount) == 0) { ?> style="display:none;" <?php } ?>
                                   title="Click to Delete Business Reference"></i>

                                <i class="fa fa-plus-circle text-primary cloneBusinessRef cloneBusinessRef_<?php echo $refCount; ?> "
                                   data-hide-class="deleteBusinessRef_<?php echo $refCount; ?>"
                                    <?php if ($refCount < $refTotalCount) { ?> style="display:none;" <?php } ?>
                                   title="Click to Add Business Reference"></i>
                            </div>
                            <div class="col-md-12">&nbsp;</div>
                            <div class="separator separator-dashed separator-border-2 separator-primary"></div>
                        </div>
                        <div class="clearfix"></div>
                        <?php $refCount++;
                    }
                    //  } ?>
                    <!-- end of business Reference -->
                <?php } ?>
                <div class="clearfix"></div>

                <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="BenBfTitle">
                    <label class="bg-secondary  py-2  col-lg-12"><b><?php echo BaseHTML::getSubSectionHeading('BEN', 'businessFinSubSection'); ?></b></label>
                </div>

                <div class="form-group row col-md-6 BenBfTitle avgMonthlyCreditcardSale_disp <?php echo loanForm::showField('avgMonthlyCreditcardSale'); ?>">
                    <?php echo loanForm::label('avgMonthlyCreditcardSale', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'avgMonthlyCreditcardSale',
                                null,
                                $tabIndex,
                                $avgMonthlyCreditcardSale,
                                BaseHTML::fieldAccess(['fNm' => 'avgMonthlyCreditcardSale', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'avgMonthlyCreditcardSale', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else { ?><b><?php echo $avgMonthlyCreditcardSale; ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBfTitle avgTotalMonthlySale_disp <?php echo loanForm::showField('avgTotalMonthlySale'); ?>">
                    <?php echo loanForm::label('avgTotalMonthlySale', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'avgTotalMonthlySale',
                                null,
                                $tabIndex,
                                $avgTotalMonthlySale,
                                BaseHTML::fieldAccess(['fNm' => 'avgTotalMonthlySale', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'avgTotalMonthlySale', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else { ?><b><?php echo $avgTotalMonthlySale; ?></b><?php } ?>
                    </div>
                </div>
                <div class="clearfix"></div>
                <div class="form-group row col-md-6 BenBfTitle annualGrossSales_disp <?php echo loanForm::showField('annualGrossSales'); ?>">
                    <?php echo loanForm::label('annualGrossSales', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'annualGrossSales',
                                null,
                                $tabIndex,
                                $annualGrossSales,
                                BaseHTML::fieldAccess(['fNm' => 'annualGrossSales', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'annualGrossSales', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else { ?><b><?php echo $annualGrossSales; ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBfTitle annualGrossProfit_disp <?php echo loanForm::showField('annualGrossProfit'); ?>">
                    <?php echo loanForm::label('annualGrossProfit', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'annualGrossProfit',
                                null,
                                $tabIndex,
                                $annualGrossProfit,
                                BaseHTML::fieldAccess(['fNm' => 'annualGrossProfit', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'annualGrossProfit', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else { ?><b><?php echo $annualGrossProfit; ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBfTitle ordinaryBusinessIncome_disp <?php echo loanForm::showField('ordinaryBusinessIncome'); ?>">
                    <?php echo loanForm::label('ordinaryBusinessIncome', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'ordinaryBusinessIncome',
                                null,
                                $tabIndex,
                                $ordinaryBusinessIncome,
                                BaseHTML::fieldAccess(['fNm' => 'ordinaryBusinessIncome', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'ordinaryBusinessIncome', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else { ?><b><?php echo $ordinaryBusinessIncome; ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBfTitle dateOfOperatingAgreement_disp <?php echo loanForm::showField('dateOfOperatingAgreement'); ?>">
                    <?php echo loanForm::label('dateOfOperatingAgreement', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend dateOfOperatingAgreement">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm preVal date_mask <?php echo BaseHTML::fieldAccess(['fNm' => 'dateOfOperatingAgreement', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                       placeholder="MM/DD/YYYY" type="text" name="dateOfOperatingAgreement"
                                       id="dateOfOperatingAgreement" value="<?php echo $dateOfOperatingAgreement ?>"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'dateOfOperatingAgreement', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                       orig-value="<?php echo $dateOfOperatingAgreement; ?>"/>
                            </div>

                        <?php } else { ?><b><?php echo $dateOfOperatingAgreement; ?></b><?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBfTitle grossAnnualRevenues_disp <?php echo loanForm::showField('grossAnnualRevenues'); ?>">
                    <?php echo loanForm::label('grossAnnualRevenues', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'grossAnnualRevenues',
                                null,
                                $tabIndex,
                                $grossAnnualRevenues,
                                BaseHTML::fieldAccess(['fNm' => 'grossAnnualRevenues', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'grossAnnualRevenues', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else {
                            echo $grossAnnualRevenues;
                        } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBfTitle showHideborrowerEntity grossIncomeLastYear_disp <?php echo loanForm::showField('grossIncomeLastYear'); ?>">
                    <?php echo loanForm::label('grossIncomeLastYear', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'grossIncomeLastYear',
                                null,
                                $tabIndex,
                                $grossIncomeLastYear,
                                BaseHTML::fieldAccess(['fNm' => 'grossIncomeLastYear', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'grossIncomeLastYear', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else {
                            echo $grossIncomeLastYear;
                        } ?>
                    </div>
                </div>
                <div class="form-group  row col-md-6 BenBfTitle showHideborrowerEntity netIncomeLastYear_disp <?php echo loanForm::showField('netIncomeLastYear'); ?>">
                    <?php echo loanForm::label('netIncomeLastYear', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'netIncomeLastYear',
                                null,
                                $tabIndex,
                                $netIncomeLastYear,
                                BaseHTML::fieldAccess(['fNm' => 'netIncomeLastYear', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'netIncomeLastYear', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else {
                            echo $netIncomeLastYear;
                        } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBfTitle showHideborrowerEntity grossIncome2YearsAgo_disp <?php echo loanForm::showField('grossIncome2YearsAgo'); ?>">
                    <?php echo loanForm::label('grossIncome2YearsAgo', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'grossIncome2YearsAgo',
                                null,
                                $tabIndex,
                                $grossIncome2YearsAgo,
                                BaseHTML::fieldAccess(['fNm' => 'grossIncome2YearsAgo', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'grossIncome2YearsAgo', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else {
                            echo $grossIncome2YearsAgo;
                        } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBfTitle showHideborrowerEntity netIncome2YearsAgo_disp <?php echo loanForm::showField('netIncome2YearsAgo'); ?>">
                    <?php echo loanForm::label('netIncome2YearsAgo', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'netIncome2YearsAgo',
                                null,
                                $tabIndex,
                                $netIncome2YearsAgo,
                                BaseHTML::fieldAccess(['fNm' => 'netIncome2YearsAgo', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'netIncome2YearsAgo', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else {
                            echo $netIncome2YearsAgo;
                        } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBfTitle showHideborrowerEntity averageBankBalance_disp <?php echo loanForm::showField('averageBankBalance'); ?>">
                    <?php echo loanForm::label('averageBankBalance', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <?php echo BaseHTML::currency(
                                'averageBankBalance',
                                null,
                                $tabIndex,
                                $averageBankBalance,
                                BaseHTML::fieldAccess(['fNm' => 'averageBankBalance', 'sArr' => $secArr, 'opt' => 'M']),
                                BaseHTML::fieldAccess(['fNm' => 'averageBankBalance', 'sArr' => $secArr, 'opt' => 'I'])
                            ); ?>
                        <?php } else {
                            echo $averageBankBalance;
                        } ?>
                    </div>
                </div>


                <div class="form-group row col-md-6  BenBfTitle <?php echo loanForm::showField('hasBusinessBankruptcy'); ?>">
                    <?php echo loanForm::label('hasBusinessBankruptcy', 'col-md-5'); ?>
                    <div class="col-md-4">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold"
                                       for="hasBusinessBankruptcyYes">
                                    <input type="radio"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'hasBusinessBankruptcy', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="hasBusinessBankruptcy" value="Yes"
                                           id="hasBusinessBankruptcyYes"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $hasBusinessBankruptcy); ?>
                                           onclick='hideAndShowSection(this.value, "Yes", "hasBusinessBankruptcyExpl");' <?php echo BaseHTML::fieldAccess(['fNm' => 'hasBusinessBankruptcy', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold"
                                       for="hasBusinessBankruptcyNo">
                                    <input type="radio" name="hasBusinessBankruptcy" value="No"
                                           id="hasBusinessBankruptcyNo"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $hasBusinessBankruptcy); ?>
                                           onclick="hideAndShowSection(this.value, 'Yes', 'hasBusinessBankruptcyExpl');" <?php echo BaseHTML::fieldAccess(['fNm' => 'hasBusinessBankruptcy', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                </label></div>
                        <?php } else { ?>
                            <h5><?php echo $hasBusinessBankruptcy; ?></h5>
                        <?php } ?>
                    </div>
                </div>

                <div class="hasBusinessBankruptcyExpl BenBfTitle form-group row col-md-6 <?php echo BaseHTML::parentFieldAccess(['fNm' => 'hasBusinessBankruptcy', 'sArr' => $secArr, 'pv' => $hasBusinessBankruptcy, 'av' => 'Yes']); ?>">
                    <?php echo loanForm::label('businessBankruptcy', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select name="businessBankruptcyBEN"
                                    id="businessBankruptcyBEN"
                                    tabindex="<?php echo $tabIndex++; ?>"
                                    class="chzn-select form-control businessBankruptcyCls <?php echo BaseHTML::fieldAccess(['fNm' => 'businessBankruptcy', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    data-placeholder="Select Status of Business Bankruptcy">
                                <option></option>
                                <?php

                                /*                 if (count($HMLOPCBasicMinSeasoningBusinessBankruptcyInfoArray ?? []) > 0) {
                                                     foreach ($HMLOPCBasicMinSeasoningBusinessBankruptcyInfoArray as $eachBankruptcyVal) { */ ?><!--
                                        <option value="<?php /*echo $eachBankruptcyVal; */ ?>" <?php /*if ($personalBankruptcy == $eachBankruptcyVal) {
                                            echo 'selected';
                                        } */ ?>><?php /*echo $globalBusinessBankruptcyCat[$eachBankruptcyVal]; */ ?></option>
                                    --><?php /*}
                                } else {*/
                                foreach ($globalBusinessBankruptcyCat as $eachBankruptcyID => $eachBankruptcyVal) { ?>
                                    <option value="<?php echo $eachBankruptcyID; ?>" <?php if ($businessBankruptcy == $eachBankruptcyID) {
                                        echo 'selected';
                                    } ?>><?php echo $eachBankruptcyVal; ?></option>
                                <?php }
                                //  } ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $globalBusinessBankruptcyCat[$businessBankruptcy];; ?></h5>
                        <?php } ?>
                    </div>
                </div>


                <div class="form-group row col-md-6 BenBfTitle showHideborrowerEntity recentNSFs_disp <?php echo loanForm::showField('recentNSFs'); ?>">
                    <?php echo loanForm::label('recentNSFs', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select name="recentNSFs"
                                    id="recentNSFs"
                                    tabindex="<?php echo $tabIndex++; ?>"
                                    class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'recentNSFs', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    data-placeholder="Select Recent NSFs">
                                <option></option>
                                <?php foreach ($globalRecentNSFsCat as $eachRecentNSFsID => $eachRecentNSFsVal) { ?>
                                    <option value="<?php echo $eachRecentNSFsID; ?>" <?php if ($recentNSFs == $eachRecentNSFsID) {
                                        echo 'selected';
                                    } ?>><?php echo $eachRecentNSFsVal; ?></option>
                                <?php } ?>
                            </select>
                        <?php } else { ?><b><?php echo $globalRecentNSFsCat[$recentNSFs] ?></b><?php } ?>
                    </div>
                </div>

                <div class="form-group row col-md-6 businessOweTaxesPrior_disp BenBfTitle <?php echo loanForm::showField('businessOweTaxesPrior'); ?>">
                    <?php echo loanForm::label('businessOweTaxesPrior', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold"
                                       for="businessOweTaxesPriorYes">
                                    <input type="radio" name="businessOweTaxesPrior"
                                           id="businessOweTaxesPriorYes"
                                           onclick="showAndHidePrePaymentPenalty(this.value, 'businessOweTaxesPriorDisOpt');"
                                           value="Yes"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'businessOweTaxesPrior', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'businessOweTaxesPrior', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php if (Strings::showField('businessOweTaxesPrior', 'fileHMLOEntityInfo') == 'Yes') {
                                        echo 'checked';
                                    } ?>><span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold"
                                       for="businessOweTaxesPriorNo">
                                    <input type="radio" name="businessOweTaxesPrior"
                                           id="businessOweTaxesPriorNo"
                                           onclick="showAndHidePrePaymentPenalty(this.value, 'businessOweTaxesPriorDisOpt');"
                                           value="No"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'businessOweTaxesPrior', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'businessOweTaxesPrior', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                           tabindex="<?php echo $tabIndex++; ?>" <?php if (Strings::showField('businessOweTaxesPrior', 'fileHMLOEntityInfo') == 'No') {
                                        echo 'checked';
                                    } ?>><span></span>No
                                </label>
                            </div>
                        <?php } else { ?>
                            <b><?php echo Strings::showField('businessOweTaxesPrior', 'fileHMLOEntityInfo') ?></b>
                        <?php } ?>
                    </div>
                </div>
                <div class="form-group row col-md-6 BenBfTitle estimatedAmountOwed_disp businessOweTaxesPriorDisOpt
                 <?php if (Strings::showField('businessOweTaxesPrior', 'fileHMLOEntityInfo') == 'Yes') {
                    echo '';
                } else {
                    echo 'd-none';
                }
                echo loanForm::showField('estimatedAmountOwed'); ?>">
                    <?php echo loanForm::label('estimatedAmountOwed', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'estimatedAmountOwed', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       type="text"
                                       name="estimatedAmountOwed"
                                       id="estimatedAmountOwed"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('estimatedAmountOwed', 'fileHMLOEntityInfo')); ?>"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       autocomplete="off"
                                       size="10"
                                       onblur="currencyConverter(this, this.value)"
                                       autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'estimatedAmountOwed', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            </div>
                        <?php } else { ?>
                            <b><?php echo Strings::showField('estimatedAmountOwed', 'fileHMLOEntityInfo') ?></b>
                        <?php } ?>

                    </div>

                </div>
                <div class="clearfix"></div>


                  <div id="entityDocs_1_1" class="col-md-12">
                    <?php
                       // echo borrowerProfile::getEntityDocsUploadHtml($PCID,$CID, null )
                    ?>
                </div>
                <!-- Entity Docs DIV-->
                <?php
                //Docs::$CBEID = $CBEID;
                //require_once __DIR__ . '/borrower/entityDocs.php';
                ?>
                <!-- Entity Docs DIV End-->


                <?php if ($hideThisField) {
                    if(PageVariables::$allowNestedEntityMembers) { //Nested Entity Members - Enabled ?>
                        <input type="hidden" name="borrowerProfileEntityInfoSync" id="borrowerProfileEntityInfoSync" value="0">
                        <div class="col-lg-12 entityField disabledFields member1Name_disp <?php echo loanForm::showField('member1Name'); ?>"
                             style="<?php echo $showBorrowerEntityDispOpt; ?>">
                            <input type="hidden" id="deletedMembersId" name="deletedMembersId">
                            <!-- Master Div Start -->
                            <div class="col-md-12 mb-5 entityMemberSection">
                                <div class="card card-custom entity_1">
                                    <div class="card-header card-header-tabs-line bg-gray-100">
                                        <div class="card-title">
                                        <span class="card-label">
                                            <b>Members/Officers</b>
                                            <span class="font-weight-bold text-danger">(List all members with 20% ownership or more)</span>
                                            <span> | <?php echo htmlspecialchars($entityName);?></span>
                                        </span>
                                        </div>
                                        <div class="card-toolbar">
                                        <span class="btn btn-sm btn-danger btn-text-primary btn-icon ml-2 tooltipClass cursor-pointer"
                                              title=""
                                              onclick="hideselectedsection('MembersOfficers', '0' );"
                                              data-original-title="Click to remove Members/Officers info">
                                            <i class=" icon-md fas fa-minus-circle"></i>
                                        </span>
                                            <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                                                  data-card-tool="toggle"
                                                  data-section="entity_1"
                                                  data-toggle="tooltip"
                                                  data-placement="top"
                                                  title=""
                                                  data-original-title="Toggle Card">
                                            <i class="ki icon-nm ki-arrow-down"></i>
                                        </span>
                                        </div>
                                    </div>
                                    <div class="card-body entity_1_body">
                                        <div class="col-md-12">
                                        <span class="font-weight-bold text-danger hide">
                                            Please add one child Member/Officer and click on the save button to continue adding more members/officers.
                                        </span>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <label class="col-md-10 font-weight-bold">
                                                    How many members/officers are there with 20%+ ownership?
                                                </label>
                                                <?php
                                                if($isBorrowerProfile) { // Borrower Profile Data?>
                                                    <div class="col-md-2">
                                                        <input
                                                                class="form-control input-sm"
                                                                type="text"
                                                                maxlength="2"
                                                                name="parentEntityMemberCount"
                                                                id="parentEntityMemberCount"
                                                                value="<?php if (count($parentCount)) echo count($parentCount); ?>"
                                                                data-row="1"
                                                                data-inc="1"
                                                                onchange="borrowerProfile.getEntityInfoMembers(this)"
                                                                autocomplete="off"
                                                        >
                                                    </div>
                                                    <?php
                                                } else { // Loan File Level Data ?>
                                                    <div class="col-md-2">
                                                        <input
                                                                class="form-control input-sm"
                                                                type="text"
                                                                maxlength="2"
                                                                name="parentEntityMemberCount"
                                                                id="parentEntityMemberCount"
                                                                value="<?php if (count($parentCount)) echo count($parentCount); ?>"
                                                                data-row="1"
                                                                data-inc="1"
                                                                data-lmrid="<?php echo cypher::myEncryption(LMRequest::$LMRId);  ?>"
                                                                data-activetab="<?php echo cypher::myEncryption(LMRequest::$activeTab); ?>"
                                                                onchange="BusinessEntitySection.parentEntityMemberFields(this, 1);"
                                                                autocomplete="off"
                                                        >
                                                    </div>
                                                <?php } ?>
                                            </div>
                                        </div>
                                        <?php
                                        if($isBorrowerProfile) {
                                            ?>
                                            <div id="entityMembersDiv_1_1"></div>
                                            <?php
                                        } else {
                                            echo entityMembers::getEntityMembers(LMRequest::$LMRId ?: 0, null, htmlspecialchars($entityName));
                                            ?>
                                            <div id="entityMembersDiv_1_1"></div>
                                            <?php
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <!-- Master Div End -->
                        </div>
                    <?php } else { //Nested Entity Members - Disabled | Old Code?>
                        <div class="col-lg-12 entityField disabledFields member1Name_disp <?php echo loanForm::showField('member1Name'); ?>"
                             style="<?php echo $showBorrowerEntityDispOpt; ?>">
                            <!-- Members/Officers -->
                            <input type="hidden" id="deletedMembersId" name="deletedMembersId">
                            <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="personalInfotitle">
                                <label class="bg-secondary  py-2  col-lg-12"><b><?php echo BaseHTML::getSubSectionHeading('BEN', 'memberOfficeSubSection'); ?></b>
                                    <span class="text-danger font-weight-bold"><?php echo BaseHTML::getSubSectionHeading('BEN', 'memberOfficeSubSection2'); ?></span>
                                </label>
                            </div>
                            <?php
                            $fieldCounter = 0;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberName', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberTitle', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberOwnership', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberAnnualSalary', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberAddress', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberPhone', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberCell', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberSSN', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberDOB', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberCreditScore', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberEmail', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberDriversLicense', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberDriversLicenseState', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberTin', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberPersonalGuarantee', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberAuthorizedSigner', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberCitizenship', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberMaritalStatus', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberMarriageDate', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberDivorceDate', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberMaidenName', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberSpouseName', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberCreditScoreDate', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberRentOrOwn', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberMonthlyRentOrMortgage', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberDateMovedAddress', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberRealEstateValue', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberRetirementAccountBalance', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberCashSavingsStocksBalance', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberCreditCardBalance', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberMortgageBalance', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberAutoLoanBalance', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;
                            if (trim(BaseHTML::fieldAccess(['fNm' => 'memberTotalNetWorth', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') $fieldCounter++;

                            for ($h = 0; $h < 10; $h++) {
                                $memberId = '';
                                $memberName = '';
                                $memberTitle = '';
                                $memberOwnership = '';
                                $memberAnnualSalary = '';
                                $memberAddress = '';
                                $memberPhone = '';
                                $memberCell = '';
                                $memberSSN = '';
                                $memberDOB = '';
                                $memberCreditScore = '';
                                $memberCitizenship = '';
                                $memberEmail = $memberDriversLicense = $memberDriversLicenseState = '';
                                $memberId = $fileMemberOfficerInfo[$h]['memberId'];
                                $memberName = $fileMemberOfficerInfo[$h]['memberName'];
                                $memberTitle = $fileMemberOfficerInfo[$h]['memberTitle'];
                                $memberOwnership = $fileMemberOfficerInfo[$h]['memberOwnership'];
                                $memberAnnualSalary = $fileMemberOfficerInfo[$h]['memberAnnualSalary'];
                                $memberAddress = $fileMemberOfficerInfo[$h]['memberAddress'];
                                $memberPhone = $fileMemberOfficerInfo[$h]['memberPhone'];
                                $memberCell = $fileMemberOfficerInfo[$h]['memberCell'];
                                $memberSSN = $fileMemberOfficerInfo[$h]['memberSSN'];
                                $memberDOB = $fileMemberOfficerInfo[$h]['memberDOB'];
                                $memberCreditScore = $fileMemberOfficerInfo[$h]['memberCreditScore'];
                                $memberCreditScoreDate = $fileMemberOfficerInfo[$h]['memberCreditScoreDate'] ?? '';
                                $memberEmail = $fileMemberOfficerInfo[$h]['memberEmail'];
                                $memberDriversLicense = $fileMemberOfficerInfo[$h]['memberDriversLicense'];
                                $memberDriversLicenseState = $fileMemberOfficerInfo[$h]['memberDriversLicenseState'];
                                $memberTin = $fileMemberOfficerInfo[$h]['memberTin'];
                                $memberPersonalGuarantee = $fileMemberOfficerInfo[$h]['memberPersonalGuarantee'];
                                $memberAuthorizedSigner = $fileMemberOfficerInfo[$h]['memberAuthorizedSigner'];
                                $memberCitizenship = $fileMemberOfficerInfo[$h]['memberCitizenship'];
                                $memberMaritalStatus = $fileMemberOfficerInfo[$h]['memberMaritalStatus'] ?? '';
                                $memberMarriageDate = $fileMemberOfficerInfo[$h]['memberMarriageDate'] ?? '';
                                $memberDivorceDate = $fileMemberOfficerInfo[$h]['memberDivorceDate'] ?? '';
                                $memberMaidenName = $fileMemberOfficerInfo[$h]['memberMaidenName'] ?? '';
                                $memberSpouseName = $fileMemberOfficerInfo[$h]['memberSpouseName'] ?? '';
                                $memberRentOrOwn = $fileMemberOfficerInfo[$h]['memberRentOrOwn'] ?? '';
                                $memberMonthlyRentOrMortgage = $fileMemberOfficerInfo[$h]['memberMonthlyRentOrMortgage'] ?? '';
                                $memberDateMovedAddress = $fileMemberOfficerInfo[$h]['memberDateMovedAddress'] ?? '';
                                $memberRealEstateValue = $fileMemberOfficerInfo[$h]['memberRealEstateValue'] ?? '';
                                $memberRetirementAccountBalance = $fileMemberOfficerInfo[$h]['memberRetirementAccountBalance'] ?? '';
                                $memberCashSavingsStocksBalance = $fileMemberOfficerInfo[$h]['memberCashSavingsStocksBalance'] ?? '';
                                $memberCreditCardBalance = $fileMemberOfficerInfo[$h]['memberCreditCardBalance'] ?? '';
                                $memberMortgageBalance = $fileMemberOfficerInfo[$h]['memberMortgageBalance'] ?? '';
                                $memberAutoLoanBalance = $fileMemberOfficerInfo[$h]['memberAutoLoanBalance'] ?? '';
                                $memberTotalNetWorth = $fileMemberOfficerInfo[$h]['memberTotalNetWorth'] ?? '';
                                $memberMaritalDisp = 'secHide';
                                $memberMaritalDisp = ($memberMaritalStatus == 'Married' || $memberMaritalStatus == 'Separated') ? BaseHTML::parentFieldAccess(['fNm' => 'memberMaritalStatus', 'sArr' => $secArr, 'pv' => $memberMaritalStatus, 'mv' => 'Married,Separated']) : 'secHide';
                                if ($memberName != '' || $memberTitle != '' || $memberOwnership > 0 || $h == 0) {
                                    ${'showMemberDispOpt'} = 'display : flex';
                                } else {
                                    ${'showMemberDispOpt'} = 'display:none';
                                }
                                $cls = '';
                                if (($h + 1) % 2 == 0) $cls = 'even';
                                ?>
                                <div class="form-group bg-light row col-md-12 py-4 MembersOfficers<?php echo $h ?>ID <?php echo $cls; ?> memOff"
                                     style="<?php echo ${'showMemberDispOpt'} ?>">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="row col-md-12">
                                            <div class="col-md-6">
                                                <?php if ($isBorrowerProfile != 1 && $fieldCounter > 0 && $h == 0) { ?>
                                                    <div class="checkbox-inline">
                                                        <label class="checkbox checkbox-outline checkbox-outline-2x checkbox-primary font-weight-bold"
                                                               for="borrowerInfo">
                                                            <input type="checkbox"
                                                                   name="borrowerInfo"
                                                                   class="borrowerInfo"
                                                                   id="borrowerInfo"
                                                                   value="1"/>
                                                            <span></span>
                                                            Same as Borrower/Applicant Info Above
                                                        </label>
                                                    </div>
                                                <?php } ?>
                                            </div>
                                            <div class="col-md-6 text-right">
                                        <span onclick="hideselectedsection('MembersOfficers', '<?php echo $h ?>' );"
                                              class="btn btn-sm btn-danger btn-text-primary btn-icon ml-2 tooltipClass cursor-pointer"
                                              title="Click to remove Members/Officers info">
                                            <i class=" icon-md fas fa-minus-circle"></i>
                                        </span>
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <div class="row col-md-12 py-2">
                                        <input type="hidden" name="memberId[]" id="memberId<?php echo $h ?>"
                                               value="<?php echo $memberId; ?>">
                                        <?php if ($hideThisField) { ?>
                                            <div class="col-md-3 mb-2 memberName_disp memOfficeField <?php echo loanForm::showField('memberName'); ?>">
                                                <?php echo loanForm::label('memberName', 'font-weight-bold', '', '', $h); ?>
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           type="text" name="memberName[]"
                                                           id="memberName<?php echo $h ?>"
                                                           value="<?php echo htmlentities($memberName); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"/>
                                                <?php } else { ?><b><?php echo $memberName; ?></b><?php } ?>
                                            </div>
                                        <?php } ?>
                                        <div class="col-md-3 mb-2 memberTitle_disp memOfficeField <?php echo loanForm::showField('memberTitle'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberTitle',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberTitle', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       type="text" name="memberTitle[]"
                                                       id="memberTitle<?php echo $h ?>"
                                                       value="<?php echo htmlentities($memberTitle); ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            <?php } else { ?><b><?php echo $memberTitle; ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberOwnership_disp memOfficeField  <?php echo loanForm::showField('memberOwnership'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberOwnership',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">%</span>
                                                    </div>
                                                    <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberOwnership', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           type="number" name="memberOwnership[]"
                                                           id="memberOwnership<?php echo $h ?>"
                                                           value="<?php if ($memberOwnership == 0) {
                                                               echo '';
                                                           } else {
                                                               echo $memberOwnership;
                                                           } ?>"
                                                           onchange="validatePercentage(this);"
                                                           tabindex="<?php echo $tabIndex++; ?>"/>
                                                </div>
                                            <?php } else { ?><b><?php echo $memberOwnership; ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberAnnualSalary_disp memOfficeField  <?php echo loanForm::showField('memberAnnualSalary'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberAnnualSalary',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberAnnualSalary', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       placeholder="0.00"
                                                       type="text"
                                                       name="memberAnnualSalary[]"
                                                       id="memberAnnualSalary<?php echo $h ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimalZeros($memberAnnualSalary); ?>"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            <?php } else { ?><b><?php echo Currency::formatDollarAmountWithDecimalZeros($memberAnnualSalary); ?></b><?php } ?>
                                        </div>
                                        <?php if ($hideThisField) { ?>
                                            <script>
                                                $(document).ready(function() {
                                                    $('#memberAddress<?php echo $h; ?>').on('input', function() {
                                                        address_lookup.InitLegacy($(this));
                                                    });
                                                });
                                            </script>
                                            <div class="col-md-3 mb-2 memberAddress_disp memOfficeField <?php echo loanForm::showField('memberAddress'); ?>">
                                                <?php echo loanForm::label2(
                                                    'memberAddress',
                                                    'font-weight-bold',
                                                    '',
                                                    '',
                                                    $h,
                                                ); ?>
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberAddress', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           type="text"
                                                           name="memberAddress[]"
                                                           data-address="memberAddress<?php echo $h; ?>"
                                                           id="memberAddress<?php echo $h ?>"
                                                           value="<?php echo htmlentities($memberAddress); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"/>
                                                <?php } else { ?><b><?php echo $memberAddress; ?></b><?php } ?>
                                            </div>
                                        <?php } ?>
                                        <?php if ($hideThisField) { ?>
                                            <div class="col-md-3 mb-2 memberPhone_disp memOfficeField <?php echo loanForm::showField('memberPhone'); ?>">
                                                <?php echo loanForm::label2(
                                                    'memberPhone',
                                                    'font-weight-bold',
                                                    '',
                                                    '',
                                                    $h,
                                                ); ?>
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control preVal input-sm mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'memberPhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           type="text" name="memberPhone[]"
                                                           id="memberPhone<?php echo $h ?>"
                                                           value="<?php echo Strings::formatPhoneNumber($memberPhone); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"/>
                                                <?php } else { ?>
                                                    <b><?php echo Strings::formatPhoneNumber($memberPhone); ?></b><?php } ?>
                                            </div>
                                        <?php } ?>
                                        <?php if ($hideThisField) { ?>
                                            <div class="col-md-3 mb-2 memberCell_disp memOfficeField <?php echo loanForm::showField('memberCell'); ?>">
                                                <?php echo loanForm::label2(
                                                    'memberCell',
                                                    'font-weight-bold',
                                                    '',
                                                    '',
                                                    $h,
                                                ); ?>
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control preVal input-sm mask_cellnew <?php echo BaseHTML::fieldAccess(['fNm' => 'memberCell', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           type="text" name="memberCell[]"
                                                           onblur="fieldValidation(this.id,this.name)"
                                                           id="memberCell<?php echo $h ?>" placeholder="(___) ___ - ____"
                                                           value="<?php echo Strings::formatPhoneNumber($memberCell) ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"/>
                                                <?php } else { ?>
                                                    <b><?php echo Strings::formatPhoneNumber($memberCell); ?></b><?php } ?>
                                            </div>
                                        <?php } ?>
                                        <?php if ($hideThisField) { ?>
                                            <div class="col-md-3 mb-2  memberSSN_disp memOfficeField  <?php echo loanForm::showField('memberSSN'); ?>">
                                                <?php echo loanForm::label2(
                                                    'memberSSN',
                                                    'font-weight-bold',
                                                    '',
                                                    '',
                                                    $h,
                                                ); ?>
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control preVal input-sm mask_ssn <?php echo BaseHTML::fieldAccess(['fNm' => 'memberSSN', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           type="text" name="memberSSN[]"
                                                           onblur="fieldValidation(this.id,this.name)"
                                                           id="memberSSN<?php echo $h ?>"
                                                           value="<?php echo Strings::formatPhoneNumber($memberSSN) ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"/>
                                                <?php } else { ?>
                                                    <b><?php echo Strings::formatSSNNumber($memberSSN); ?></b><?php } ?>
                                            </div>
                                        <?php } ?>

                                        <div class="col-md-3 mb-2  memberDOB_disp memOfficeField <?php echo loanForm::showField('memberDOB'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberDOB',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend memberDOB<?php echo $h ?>  ">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                                    </div>
                                                    <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberDOB', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass onChangeEntity"
                                                           type="text" name="memberDOB[]"
                                                           id="memberDOB<?php echo $h ?>"
                                                           value="<?php echo Dates::formatDateWithRE($memberDOB, 'YMD', 'm/d/Y'); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>" placeholder="MM/DD/YYYY"/>
                                                </div>

                                            <?php } else { ?>
                                                <b><?php echo Dates::formatDateWithRE($memberDOB, 'YMD', 'm/d/Y'); ?></b><?php } ?>
                                        </div>
                                        <?php if ($hideThisField) { ?>
                                            <div class="col-md-3 mb-2 memberEmail_disp <?php echo loanForm::showField('memberEmail'); ?>">
                                                <?php echo loanForm::label2(
                                                    'memberEmail',
                                                    'font-weight-bold',
                                                    '',
                                                    '',
                                                    $h,
                                                ); ?>
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           type="email" name="memberEmail[]"
                                                           onblur="fieldValidation(this.id,this.name)"
                                                           id="memberEmail<?php echo $h ?>"
                                                           value="<?php echo $memberEmail ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"/>
                                                <?php } else { ?><b><?php echo $memberEmail; ?></b><?php } ?>
                                            </div>
                                        <?php } ?>
                                        <div class="col-md-3 mb-2  memberCreditScore_disp memOfficeField <?php echo loanForm::showField('memberCreditScore'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberCreditScore',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <input type="number" name="memberCreditScore[]"
                                                       id="memberCreditScore<?php echo $h ?>"
                                                       class="form-control preVal input-sm memberCreditScore <?php echo BaseHTML::fieldAccess(['fNm' => 'memberCreditScore', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       value="<?php echo $memberCreditScore; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            <?php } else { ?>
                                                <b><?php echo $memberCreditScore; ?></b>
                                            <?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberCreditScoreDate_disp memOfficeField <?php echo loanForm::showField('memberCreditScoreDate'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberCreditScoreDate',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend memberCreditScoreDate<?php echo $h ?>  ">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                                    </div>
                                                    <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberCreditScoreDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                           type="text" name="memberCreditScoreDate[]"
                                                           id="memberCreditScoreDate<?php echo $h ?>"
                                                           value="<?php echo Dates::formatDateWithRE($memberCreditScoreDate, 'YMD', 'm/d/Y'); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>" placeholder="MM/DD/YYYY"/>
                                                </div>

                                            <?php } else { ?>
                                                <b><?php echo Dates::formatDateWithRE($memberCreditScoreDate, 'YMD', 'm/d/Y'); ?></b><?php } ?>
                                        </div>
                                        <div class="mb-2 col-md-3 mb-2 memberRentOrOwn_disp memOfficeField <?php echo loanForm::showField('memberRentOrOwn'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberRentOrOwn',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <select class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'memberRentOrOwn', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                        name="memberRentOrOwn[]"
                                                        id="memberRentOrOwn<?php echo $h ?>">
                                                    <option value=''> - Select -</option>
                                                    <option value='Rent' <?php echo Arrays::isSelected('Rent', $memberRentOrOwn); ?> >
                                                        Rent
                                                    </option>
                                                    <option value='Own' <?php echo Arrays::isSelected('Own', $memberRentOrOwn); ?>>
                                                        Own
                                                    </option>
                                                </select>
                                            <?php } else { ?><b><?php echo $memberRentOrOwn; ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberMonthlyRentOrMortgage_disp memOfficeField  <?php echo loanForm::showField('memberMonthlyRentOrMortgage'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberMonthlyRentOrMortgage',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberMonthlyRentOrMortgage', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       placeholder="0.00"
                                                       type="text"
                                                       name="memberMonthlyRentOrMortgage[]"
                                                       id="memberMonthlyRentOrMortgage<?php echo $h ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($memberMonthlyRentOrMortgage); ?>"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            </div>
                                            <?php } else { ?><b><?php echo Currency::formatDollarAmountWithDecimal($memberMonthlyRentOrMortgage); ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberDateMovedAddress_disp memOfficeField <?php echo loanForm::showField('memberDateMovedAddress'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberDateMovedAddress',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend memberDateMovedAddress<?php echo $h ?>  ">
                                                        <span class="input-group-text">
                                                            <i class="fa fa-calendar text-primary"></i>
                                                        </span>
                                                    </div>
                                                    <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberDateMovedAddress', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                           type="text" name="memberDateMovedAddress[]"
                                                           id="memberDateMovedAddress<?php echo $h ?>"
                                                           value="<?php echo Dates::formatDateWithRE($memberDateMovedAddress, 'YMD', 'm/d/Y'); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>" placeholder="MM/DD/YYYY"/>
                                                </div>
                                            <?php } else { ?>
                                                <b><?php echo Dates::formatDateWithRE($memberDateMovedAddress, 'YMD', 'm/d/Y'); ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberRealEstateValue_disp memOfficeField <?php echo loanForm::showField('memberRealEstateValue'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberRealEstateValue',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberRealEstateValue', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       placeholder="0.00"
                                                       type="text"
                                                       name="memberRealEstateValue[]"
                                                       id="memberRealEstateValue<?php echo $h ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($memberRealEstateValue); ?>"
                                                       data-index="<?php echo $h;?>"
                                                       onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this);"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            </div>
                                            <?php } else { ?><b><?php echo Currency::formatDollarAmountWithDecimal($memberRealEstateValue); ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberRetirementAccountBalance_disp memOfficeField <?php echo loanForm::showField('memberRetirementAccountBalance'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberRetirementAccountBalance',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberRetirementAccountBalance', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       placeholder="0.00"
                                                       type="text"
                                                       name="memberRetirementAccountBalance[]"
                                                       id="memberRetirementAccountBalance<?php echo $h ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($memberRetirementAccountBalance); ?>"
                                                       data-index="<?php echo $h;?>"
                                                       onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this);"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            </div>
                                            <?php } else { ?><b><?php echo Currency::formatDollarAmountWithDecimal($memberRetirementAccountBalance); ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberCashSavingsStocksBalance_disp memOfficeField <?php echo loanForm::showField('memberCashSavingsStocksBalance'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberCashSavingsStocksBalance',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberCashSavingsStocksBalance', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       placeholder="0.00"
                                                       type="text"
                                                       name="memberCashSavingsStocksBalance[]"
                                                       id="memberCashSavingsStocksBalance<?php echo $h ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($memberCashSavingsStocksBalance); ?>"
                                                       data-index="<?php echo $h;?>"
                                                       onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this);"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            </div>
                                            <?php } else { ?><b><?php echo Currency::formatDollarAmountWithDecimal($memberCashSavingsStocksBalance); ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberCreditCardBalance_disp memOfficeField <?php echo loanForm::showField('memberCreditCardBalance'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberCreditCardBalance',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberCreditCardBalance', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       placeholder="0.00"
                                                       type="text"
                                                       name="memberCreditCardBalance[]"
                                                       id="memberCreditCardBalance<?php echo $h ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($memberCreditCardBalance); ?>"
                                                       data-index="<?php echo $h;?>"
                                                       onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this);"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            </div>
                                            <?php } else { ?><b><?php echo Currency::formatDollarAmountWithDecimal($memberCreditCardBalance); ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberMortgageBalance_disp memOfficeField <?php echo loanForm::showField('memberMortgageBalance'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberMortgageBalance',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberMortgageBalance', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       placeholder="0.00"
                                                       type="text"
                                                       name="memberMortgageBalance[]"
                                                       id="memberMortgageBalance<?php echo $h ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($memberMortgageBalance); ?>"
                                                       data-index="<?php echo $h;?>"
                                                       onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this);"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            </div>
                                            <?php } else { ?><b><?php echo Currency::formatDollarAmountWithDecimal($memberMortgageBalance); ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberAutoLoanBalance_disp memOfficeField <?php echo loanForm::showField('memberAutoLoanBalance'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberAutoLoanBalance',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberAutoLoanBalance', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       placeholder="0.00"
                                                       type="text"
                                                       name="memberAutoLoanBalance[]"
                                                       id="memberAutoLoanBalance<?php echo $h ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($memberAutoLoanBalance); ?>"
                                                       data-index="<?php echo $h;?>"
                                                       onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this);"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            </div>
                                            <?php } else { ?><b><?php echo Currency::formatDollarAmountWithDecimal($memberAutoLoanBalance); ?></b><?php } ?>
                                        </div>
                                        <div class="col-md-3 mb-2 memberTotalNetWorth_disp memOfficeField <?php echo loanForm::showField('memberTotalNetWorth'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberTotalNetWorth',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberTotalNetWorth', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       placeholder="0.00"
                                                       type="text" readonly
                                                       id="memberTotalNetWorth<?php echo $h ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($memberTotalNetWorth); ?>"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            </div>
                                            <?php } else { ?><b><?php echo Currency::formatDollarAmountWithDecimal($memberTotalNetWorth); ?></b><?php } ?>
                                        </div>

                                        <div class="mb-2 col-md-3 memberMaritalStatus_disp  <?php echo loanForm::showField('memberMaritalStatus'); ?> ">
                                            <?php echo loanForm::label2(
                                                'memberMaritalStatus',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <div class="radio-inline">
                                                <label class="radio radio-solid font-weight-bold"
                                                       for="memberMaritalStatus0_<?php echo $h ?>">
                                                    <input type="radio"
                                                           onclick="businessEntity.showHideMaritalFields(this.value,'maritalFields_<?php echo $h ?>');"
                                                           name="memberMaritalStatus_<?php echo $h ?>"
                                                           id="memberMaritalStatus0_<?php echo $h ?>"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberMaritalStatus', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           value="Unmarried" tabindex="373"
                                                        <?php echo Strings::isChecked('Unmarried', $memberMaritalStatus); ?>><span></span>Unmarried
                                                </label>
                                                <label class="radio radio-solid font-weight-bold"
                                                       for="memberMaritalStatus1_<?php echo $h ?>">
                                                    <input type="radio" id="memberMaritalStatus1_<?php echo $h ?>"
                                                           onclick="businessEntity.showHideMaritalFields(this.value,'maritalFields_<?php echo $h ?>');"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberMaritalStatus', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           name="memberMaritalStatus_<?php echo $h ?>"
                                                           value="Married" tabindex="374"
                                                        <?php echo Strings::isChecked('Married', $memberMaritalStatus); ?>><span></span>Married
                                                </label>
                                                <label class="radio radio-solid font-weight-bold"
                                                       for="memberMaritalStatus2_<?php echo $h ?>">
                                                    <input type="radio" id="memberMaritalStatus2_<?php echo $h ?>"
                                                           onclick="businessEntity.showHideMaritalFields(this.value,'maritalFields_<?php echo $h ?>');"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberMaritalStatus', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           name="memberMaritalStatus_<?php echo $h ?>"
                                                           value="Separated" tabindex="374"
                                                        <?php echo Strings::isChecked('Separated', $memberMaritalStatus); ?>><span></span>Separated
                                                </label>
                                            </div>

                                        </div>

                                        <div class="mb-2 col-md-3 memberMarriageDate_disp <?php echo $memberMaritalDisp;
                                        echo loanForm::showField('memberMarriageDate'); ?> maritalFields_<?php echo $h; ?>">
                                            <?php echo loanForm::label2(
                                                'memberMarriageDate',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend memberMarriageDate<?php echo $h ?>  ">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                                    </div>
                                                    <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberMarriageDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass onChangeEntity"
                                                           type="text" name="memberMarriageDate[]"
                                                           id="memberMarriageDate<?php echo $h ?>" placeholder="MM/DD/YYYY"
                                                           value="<?php echo Dates::formatDateWithRE($memberMarriageDate, 'YMD', 'm/d/Y');; ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"/>
                                                </div>
                                            <?php } else { ?>
                                                <b><?php echo Dates::formatDateWithRE($memberMarriageDate, 'YMD', 'm/d/Y'); ?></b><?php } ?>
                                        </div>

                                        <div class="mb-2 col-md-3 memberDivorceDate_disp <?php echo $memberMaritalDisp;
                                        echo loanForm::showField('memberDivorceDate'); ?> maritalFields_<?php echo $h; ?>">
                                            <?php echo loanForm::label2(
                                                'memberDivorceDate',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend memberDivorceDate<?php echo $h ?>  ">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                                    </div>
                                                    <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberDivorceDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass onChangeEntity"
                                                           type="text" name="memberDivorceDate[]"
                                                           id="memberDivorceDate<?php echo $h ?>" placeholder="MM/DD/YYYY"
                                                           value="<?php echo Dates::formatDateWithRE($memberDivorceDate, 'YMD', 'm/d/Y');; ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"/>
                                                </div>
                                            <?php } else { ?>
                                                <b><?php echo Dates::formatDateWithRE($memberDivorceDate, 'YMD', 'm/d/Y'); ?></b><?php } ?>
                                        </div>
                                        <div class="mb-2 col-md-3 memberMaidenName_disp <?php echo $memberMaritalDisp;
                                        echo loanForm::showField('memberMaidenName'); ?> maritalFields_<?php echo $h; ?>">
                                            <?php echo loanForm::label2(
                                                'memberMaidenName',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberMaidenName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       type="text" name="memberMaidenName[]"
                                                       id="memberMaidenName<?php echo $h ?>"
                                                       value="<?php echo $memberMaidenName; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            <?php } else { ?>
                                                <b><?php echo $memberMaidenName; ?></b><?php } ?>
                                        </div>
                                        <div class="mb-2 col-md-3 memberSpouseName_disp <?php echo $memberMaritalDisp;
                                        echo loanForm::showField('memberSpouseName'); ?> maritalFields_<?php echo $h; ?>">
                                            <?php echo loanForm::label2(
                                                'memberSpouseName',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberSpouseName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       type="text" name="memberSpouseName[]"
                                                       id="memberSpouseName<?php echo $h ?>"
                                                       value="<?php echo $memberSpouseName; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            <?php } else { ?>
                                                <b><?php echo $memberSpouseName; ?></b><?php } ?>
                                        </div>

                                        <div class="mb-2 col-md-3 mb-2 memberDriversLicenseState_disp memOfficeField <?php echo loanForm::showField('memberDriversLicenseState'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberDriversLicenseState',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <select class="form-control input-sm preVal <?php echo BaseHTML::fieldAccess(['fNm' => 'memberDriversLicenseState', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                        name="memberDriversLicenseState[]"
                                                        onchange="fieldValidation(this.id,this.name);"
                                                        id="memberDriversLicenseState<?php echo $h ?>">
                                                    <option value=''> - Select -</option>
                                                    <?php
                                                    for ($j = 0; $j < count($stateArray); $j++) {
                                                        $sOpt = '';
                                                        $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $memberDriversLicenseState);
                                                        echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else { ?><b><?php echo $memberDriversLicenseState; ?></b><?php } ?>
                                        </div>
                                        <?php if ($hideThisField) { ?>
                                            <div class="col-md-3 mb-2 memberDriversLicense_disp memOfficeField <?php echo loanForm::showField('memberDriversLicense'); ?>">
                                                <?php echo loanForm::label2(
                                                    'memberDriversLicense',
                                                    'font-weight-bold',
                                                    '',
                                                    '',
                                                    $h,
                                                ); ?>
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberDriversLicense', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           type="text" name="memberDriversLicense[]"
                                                           onblur="fieldValidation(this.id,this.name)"
                                                           id="memberDriversLicense<?php echo $h ?>"
                                                           value="<?php echo htmlentities($memberDriversLicense); ?>"
                                                           maxlength="20"
                                                           tabindex="<?php echo $tabIndex++; ?>"/>
                                                <?php } else { ?><b><?php echo $memberDriversLicense; ?></b><?php } ?>
                                            </div>
                                        <?php } ?>
                                        <div class="mb-2 col-md-3 mb-2 memberTin_disp memOfficeField  <?php echo loanForm::showField('memberTin'); ?>">
                                            <?php echo loanForm::label2(
                                                'memberTin',
                                                'font-weight-bold',
                                                '',
                                                '',
                                                $h,
                                                'Member TIN'
                                            ); ?>
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'memberTin', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       type="number" name="memberTin[]"
                                                       id="memberTin<?php echo $h ?>"
                                                       value="<?php echo htmlentities($memberTin); ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            <?php } else { ?><b><?php echo $memberTin; ?></b><?php } ?>
                                        </div>
                                        <div class="mb-2 col-md-3 memberPersonalGuarantee_disp  <?php echo loanForm::showField('memberPersonalGuarantee'); ?> ">
                                            <div class="form-group row">
                                                <?php echo loanForm::label2(
                                                    'memberPersonalGuarantee',
                                                    'font-weight-bold',
                                                    '',
                                                    '',
                                                    $h,
                                                ); ?>
                                                <div class="col-md-6">
                                                    <div class="radio-inline">
                                                        <label class="radio radio-solid font-weight-bold"
                                                               for="memberPersonalGuaranteeYes_<?php echo $h ?>">
                                                            <input type="radio"
                                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberPersonalGuarantee', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                   name="memberPersonalGuarantee_<?php echo $h ?>"
                                                                   id="memberPersonalGuaranteeYes_<?php echo $h ?>"
                                                                   value="Yes"
                                                                   tabindex="373"
                                                                <?php echo Strings::isChecked('Yes', $memberPersonalGuarantee); ?>><span></span>Yes
                                                        </label>
                                                        <label class="radio radio-solid font-weight-bold"
                                                               for="memberPersonalGuaranteeNo_<?php echo $h ?>">
                                                            <input type="radio"
                                                                   id="memberPersonalGuaranteeNo_<?php echo $h ?>"
                                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberPersonalGuarantee', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                   name="memberPersonalGuarantee_<?php echo $h ?>"
                                                                   value="No" tabindex="374"
                                                                <?php echo Strings::isChecked('No', $memberPersonalGuarantee); ?>><span></span>No
                                                        </label></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-2 col-md-3 memberAuthorizedSigner_disp  <?php echo loanForm::showField('memberAuthorizedSigner'); ?> ">
                                            <div class="form-group row">
                                                <?php echo loanForm::label2(
                                                    'memberAuthorizedSigner',
                                                    'font-weight-bold',
                                                    '',
                                                    '',
                                                    $h,
                                                ); ?>
                                                <div class="col-md-6">
                                                    <div class="radio-inline">
                                                        <label class="radio radio-solid font-weight-bold"
                                                               for="memberAuthorizedSignerYes_<?php echo $h ?>">
                                                            <input type="radio"
                                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberAuthorizedSigner', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                   name="memberAuthorizedSigner_<?php echo $h ?>"
                                                                   id="memberAuthorizedSignerYes_<?php echo $h ?>"
                                                                   value="Yes"
                                                                   tabindex="373"
                                                                <?php echo Strings::isChecked('Yes', $memberAuthorizedSigner); ?>><span></span>Yes
                                                        </label>
                                                        <label class="radio radio-solid font-weight-bold"
                                                               for="memberAuthorizedSignerNo_<?php echo $h ?>">
                                                            <input type="radio"
                                                                   id="memberAuthorizedSignerNo_<?php echo $h ?>"
                                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberAuthorizedSigner', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                   name="memberAuthorizedSigner_<?php echo $h ?>" value="No"
                                                                   tabindex="374"
                                                                <?php echo Strings::isChecked('No', $memberAuthorizedSigner); ?>><span></span>No
                                                        </label></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-2 col-md-3 memberCitizenship_disp  <?php echo loanForm::showField('memberCitizenship'); ?> ">
                                            <div class="form-group row">
                                                <?php echo loanForm::label2(
                                                    'memberCitizenship',
                                                    'font-weight-bold',
                                                    '',
                                                    '',
                                                    $h,
                                                ); ?>
                                                <div class="col-md-8">
                                                    <div class="radio-inline">
                                                        <label class="radio radio-solid font-weight-bold"
                                                               for="memberCitizenship0_<?php echo $h ?>">
                                                            <input type="radio"
                                                                   name="memberCitizenship_<?php echo $h ?>"
                                                                   id="memberCitizenship0_<?php echo $h ?>"
                                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                   value="U.S. Citizen" tabindex="373"
                                                                <?php echo Strings::isChecked('U.S. Citizen', $memberCitizenship); ?>><span></span>U.S.
                                                            Citizen
                                                        </label>
                                                        <label class="radio radio-solid font-weight-bold"
                                                               for="memberCitizenship1_<?php echo $h ?>">
                                                            <input type="radio" id="memberCitizenship1_<?php echo $h ?>"
                                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                   name="memberCitizenship_<?php echo $h ?>"
                                                                   value="Perm Resident Alien" tabindex="374"
                                                                <?php echo Strings::isChecked('Perm Resident Alien', $memberCitizenship); ?>><span></span>Perm
                                                            Resident
                                                        </label>
                                                        <label class="radio radio-solid font-weight-bold"
                                                               for="memberCitizenship2_<?php echo $h ?>">
                                                            <input type="radio" id="memberCitizenship2_<?php echo $h ?>"
                                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                   name="memberCitizenship_<?php echo $h ?>"
                                                                   value="Non-Perm Resident Alien" tabindex="374"
                                                                <?php echo Strings::isChecked('Non-Perm Resident Alien', $memberCitizenship); ?>><span></span>Non-Perm
                                                            Resident
                                                        </label>
                                                        <label class="radio radio-solid font-weight-bold"
                                                               for="memberCitizenship3_<?php echo $h ?>">
                                                            <input type="radio" id="memberCitizenship3_<?php echo $h ?>"
                                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'memberCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                   name="memberCitizenship_<?php echo $h ?>"
                                                                   value="Foreign National" tabindex="374"
                                                                <?php echo Strings::isChecked('Foreign National', $memberCitizenship); ?>><span></span>
                                                            Foreign National
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                            <div class="form-group col-lg-12">
                                <?php if ($allowToEdit) { ?>
                                    <span>
                                <label class="font-weight-bold">Add More Members/Officers Info</label>
                                    <span onclick="showAndHidePropertyValuationInfo('MembersOfficers', '10', 'add');"
                                          class="btn btn-sm btn-success btn-text-primary btn-icon ml-2 tooltipClass cursor-pointer"
                                          title="Click to add Members/Officers info">
                                        <i class=" icon-md fas fa-plus "></i>
                                    </span>
                                </span>
                                <?php } ?>
                            </div>
                            <!-- // Members/Officers // -->
                        </div>
                    <?php }
                } ?>

            </div>
            <div class="clearfix"></div>
            <div class=" col-md-6 corporateSecretaryName_disp entityField memOfficeField  <?php echo loanForm::showField('corporateSecretaryName'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label2(
                        'corporateSecretaryName',
                        'col-md-5'
                    ); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'corporateSecretaryName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text"
                                   name="corporateSecretaryName"
                                   id="corporateSecretaryName"
                                   value="<?php echo htmlentities($corporateSecretaryName); ?>"
                                   tabindex="<?php echo $tabIndex++; ?>"/>
                        <?php } else { ?>
                            <b><?php echo $corporateSecretaryName; ?></b>
                        <?php } ?>
                    </div>
                </div>
            </div>


            <div class="col-md-12 entityNotes_disp  entityField <?php echo loanForm::showField('entityNotes'); ?>"
                 style="<?php echo $showBorrowerEntityDispOpt; ?>">
                <div class="form-group row ">
                    <?php echo loanForm::label2(
                        'entityNotes',
                        'col-md-5'
                    ); ?>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <textarea
                                class="form-control preVal input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'entityNotes', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                name="entityNotes" id="entityNotes" rows="3" cols="50"
                                tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'entityNotes', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo $entityNotes ?></textarea>
                        <?php } else { ?><b><?php echo $entityNotes; ?></b><?php } ?>
                    </div>
                </div>
            </div>


            <input type="hidden" class="ischanged" id="ischanged" name="ischanged" value="0">
            <!-- check for entity info is changed or not -->
        </div>
    </div>
</div>
<!-- Business Entity Section End -->

<script src="/backoffice/LMRequest/js/businessEntitySection.js?<?php echo CONST_JS_VERSION; ?>"></script>

<style>
    .radio > span {
        border-color: #3699ff;
    }
</style>
<!-- businessEntitySection.php -->
