<?php
global $selClientId, $PCID, $oClient, $clientFullName, $saveUrl;

use models\composite\oClient\getMembersOfficers;
use models\composite\oClient\getPCClientEntityInfo;
use models\Controllers\loanForm;
use models\cypher;
use models\lendingwise\tblPCClientEntityMembers;
use models\myFileInfo\PCInfo;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
use pages\backoffice\borrowerProfile\borrowerProfile;
use pages\backoffice\borrowerProfile\classes\borrowerProfileEntityInfo;

$PCClientEntityInfoArray = [];
$stateArray = $membersOfficersArray = [];


$stateArray = Arrays::fetchStates();


if ($selClientId > 0 && $PCID > 0) {
    $PCClientEntityInfoArray = getPCClientEntityInfo::getReport(['PCID' => $PCID, 'CID' => $selClientId]);
    $membersOfficersInfoArray = getMembersOfficers::getReport(['CID' => $selClientId]);
}
$encPCID = cypher::myEncryption($PCID);
$encCID = cypher::myEncryption($selClientId);
//Nested Entity Members Save
$PC = PCInfo::getReport($PCID);
$allowNestedEntityMembers = $PC->allowNestedEntityMembers ?? 0;
?>
<style>
    #ui-datepicker-div {
        z-index: 50111 !important;
    }
</style>

<div id="buttonDisp" class="pl-2 mb-2">
    <!-- <a class="button tip-bottom" style="text-decoration:none;" title ="Click to add new Entity" href="<?php echo CONST_URL_POPS; ?>PCClientEntityInfo.php" id='CID=<?php echo cypher::myEncryption($selClientId) ?>&amp;PCID=<?php echo cypher::myEncryption($PCID) ?>' name="<?php echo $clientFullName; ?> > Create/Edit Client Entity Info">Add New</a> -->
    <a class="tooltipClass" title="Click to add new Entity" data-toggle="tooltip" data-html="true"
       href="javascript:entityInfoPopup('<?php echo $encCID; ?>', '', '<?php echo $encPCID; ?>', '');">
        <button type="button" class="btn btn-info btn-sm">Add New</button>
    </a>

</div>
<?php
$entityCnt = '';
$CBEID = 0;
$entityCnt = count($PCClientEntityInfoArray);

$tempClientEntityInfo = [];
$k = 1;
for ($ce = 0; $ce < $entityCnt; $ce++) {
    $entityType = '';
    $ENINo = '';
    $MOID = 0;
    $CBEID = 0;
    $entityNotes = '';
    $tabIndex = 1;
    $entityName = '';
    $entityAddress = '';
    $entityCity = '';
    $entityState = '';
    $entityZip = '';
    $entityWebsite = '';
    $businessTypeEF = '';
    $organizationalRef = '';
    $tempClientEntityInfo = $PCClientEntityInfoArray[$ce];
    $tradeName = '';
    $dateOfFormation = '';
    $dateOfOperatingAgreement = '';
    $memberCreditScore = '';
    $entityLocation = '';
    $statesRegisterdIn = '';
    $borrowerType = '';
    if (count($tempClientEntityInfo) > 0) {
        $MOID = trim($tempClientEntityInfo['MOID']);
        $CBEID = trim($tempClientEntityInfo['CBEID']);
        $entityName = trim($tempClientEntityInfo['entityName']);
        $entityType = trim($tempClientEntityInfo['entityType']);
        $ENINo = trim($tempClientEntityInfo['ENINo']);
        $entityAddress = trim($tempClientEntityInfo['entityAddress']);
        $entityCity = trim($tempClientEntityInfo['entityCity']);
        $entityState = trim($tempClientEntityInfo['entityState']);
        $entityZip = trim($tempClientEntityInfo['entityZip']);
        $entityStateOfFormation = trim($tempClientEntityInfo['entityStateOfFormation']);
        $statesRegisterdIn = trim($tempClientEntityInfo['statesRegisterdIn']);
        $entityNotes = trim($tempClientEntityInfo['entityNotes']);
        $entityWebsite = trim($tempClientEntityInfo['entityWebsite']);
        $businessTypeEF = trim($tempClientEntityInfo['businessTypeEF']);
        $organizationalRef = trim($tempClientEntityInfo['organizationalRef']);
        $tradeName = trim($tempClientEntityInfo['tradeName']);
        $dateOfFormation = trim($tempClientEntityInfo['dateOfFormation']);
        $dateOfOperatingAgreement = trim($tempClientEntityInfo['dateOfOperatingAgreement']);
        $memberCreditScore = trim($tempClientEntityInfo['memberCreditScore']);
        $entityLocation = trim($tempClientEntityInfo['entityLocation']);
        borrowerProfile::$PCID = trim($tempClientEntityInfo['PCID']);
        $borrowerType = trim($tempClientEntityInfo['borrowerType']);;
    }

    $membersOfficersArray = [];
    if (array_key_exists($CBEID, $membersOfficersInfoArray)) {
        $membersOfficersArray = $membersOfficersInfoArray[$CBEID];
    }

    if (Dates::IsEmpty($dateOfFormation)) {
        $dateOfFormation = '';
    } else {
        $dateOfFormation = Dates::formatDateWithRE($dateOfFormation, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($dateOfOperatingAgreement)) {
        $dateOfOperatingAgreement = '';
    } else {
        $dateOfOperatingAgreement = Dates::formatDateWithRE($dateOfOperatingAgreement, 'YMD', 'm/d/Y');
    }

    $parentCount = borrowerProfileEntityInfo::getRootMembers($CID, $CBEID, null);
    $encCBEID = cypher::myEncryption($CBEID);
    $entitySectionID = 'clientEntityInfoDivId_' . $CBEID;
    ?>
    <div id="<?php echo $entitySectionID ?>">
        <div class="card card-custom mb-2" id="card_<?php echo $CBEID; ?>">
            <div class="card-header bg-primary-o-40" id="heading_<?php echo $CBEID; ?>">
                <div class="card-title entityInfoCnt h4"
                     data-toggle="collapse"
                     data-entity-name="<?php echo $entityName; ?>"
                     data-target="#collapse_<?php echo $CBEID; ?>">
                    <?php
                    if ($borrowerType == 'Entity') {
                        echo $borrowerType . ' Info : (' . $entityName . ') ';
                    } else {
                        echo $borrowerType;
                    }
                    ?>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);"
                       class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 divOpenClose"
                       data-toggle="tooltip" data-placement="top"
                       data-body-id="<?php echo 'body_' . $CBEID ?>"
                       title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                    <a href="javascript:deletePCClientEntityInfoConfirm('<?php echo $encCID; ?>', '<?php echo $encCBEID; ?>', '<?php echo $encPCID; ?>', '<?php echo $entitySectionID ?>');"
                       class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2"
                       data-toggle="tooltip" data-placement="top" title="Click to delete client entity Info">
                        <i class="fas fa-trash-alt  "></i>
                    </a>
                    <a href="javascript:entityInfoPopup('<?php echo $encCID; ?>', '<?php echo $encCBEID; ?>', '<?php echo $encPCID; ?>', '<?php echo $entitySectionID ?>');"
                       class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2"
                       data-placement="top"
                       title="Click to edit client entity Info">
                        <i class="fas fa-pencil-alt"></i>
                    </a>
                </div>
            </div>
            <div class="card-body" style="display: none;" id="<?php echo 'body_' . $CBEID ?>">
                <div class="table-responsive">
                    <table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
                        <tr>
                            <td class="font-weight-bold">Entity Name</td>
                            <td>
                                <?php echo $entityName; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Entity Type</td>
                            <td><?php echo $entityType; ?></td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">EIN #</td>
                            <td><?php echo $ENINo; ?></td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Organizational Ref #</td>
                            <td><?php echo $organizationalRef; ?></td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Address</td>
                            <td><?php echo $entityAddress; ?></td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">City</td>
                            <td><?php echo $entityCity; ?></td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">State</td>
                            <td><?php echo $entityState; ?></td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Zip Code</td>
                            <td><?php echo $entityZip; ?></td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">State Of Formation</td>
                            <td><?php echo $entityStateOfFormation; ?></td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">States Registered In</td>
                            <td><?php echo $statesRegisterdIn; ?></td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Website</td>
                            <td><?php echo $entityWebsite; ?></td>
                        </tr>
                        <?php if($allowNestedEntityMembers) { ?>
                        <tr>
                            <td colspan="2" class="font-weight-bold">
                                <h5>Members/Officers</h5>
                                <span class="font-weight-bold text-danger">(List all members with 20% ownership or more)</span>
                                <span> | <?php echo htmlspecialchars($entityName);?></span>
                                <div class="col-md-12">
                                        <span class="font-weight-bold text-danger">
                                            Please add one child Member/Officer and click on the save button to continue adding more members/officers.
                                        </span>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group row">
                                        <label class="col-md-10 font-weight-bold">
                                            How many members/officers are there with 20%+ ownership?
                                        </label>
                                        <div class="col-md-2">
                                            <input
                                                    class="form-control input-sm"
                                                    type="text"
                                                    maxlength="2"
                                                    name="parentCount"
                                                    id="parentCount"
                                                    value="<?php if (count($parentCount)) echo count($parentCount); ?>"
                                                    data-row="1"
                                                    data-inc="1"
                                                    data-moid="<?php echo cypher::myEncryption($MOID);  ?>"
                                                    readonly
                                                    onchange=""
                                            >
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <table style="width: 100%;">
                                    <div class="col-md-12">
                                        <?php
                                        if($membersOfficersArray) {
                                            $CID = $membersOfficersArray[0]['CID'];
                                            $CBEID = $membersOfficersArray[0]['CBEID'];
                                            borrowerProfile::$isPageView = 1;
                                            echo borrowerProfile::getMembers($CID, $CBEID, null);
                                            borrowerProfile::$html = '';
                                        }
                                        ?>
                                    </div>
                                </table>
                            </td>
                        </tr>
                        <?php } else { ?>
                            <tr>
                                <td colspan="2" class="font-weight-bold"><h5>Members/Officers</h5></td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <table style="width: 100%">
                                        <?php
                                        for ($h = 0; $h < count($membersOfficersArray); $h++) {
                                            if ($h % 2 == 0) $clsRow = "class=\"even\"";
                                            else            $clsRow = "class=\"odd\"";
                                            ?>
                                            <tr>
                                                <td class="font-weight-bold">Name</td>
                                                <td><?php echo $membersOfficersArray[$h]['memberName']; ?></td>
                                                <td class="font-weight-bold">Title</td>
                                                <td><?php echo $membersOfficersArray[$h]['memberTitle']; ?>
                                                </td>
                                                <td class="font-weight-bold">% of Ownership</td>
                                                <td><?php echo $membersOfficersArray[$h]['memberOwnership']; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="font-weight-bold">Annual Salary/Compensation</td>
                                                <td><?php echo $membersOfficersArray[$h]['memberAnnualSalary']; ?>
                                                </td>
                                                <td class="font-weight-bold">Address</td>
                                                <td><?php echo $membersOfficersArray[$h]['memberAddress']; ?>
                                                </td>
                                                <td class="font-weight-bold">Home Phone</td>
                                                <td>
                                                    <?php echo Strings::formatPhoneNumber($membersOfficersArray[$h]['memberPhone']); ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="font-weight-bold">Cell No</td>
                                                <td>
                                                    <?php echo Strings::formatPhoneNumber($membersOfficersArray[$h]['memberCell']); ?>
                                                </td>
                                                <td class="font-weight-bold">SSN</td>
                                                <td>
                                                    <?php echo Strings::formatSSNNumber($membersOfficersArray[$h]['memberSSN']); ?>
                                                </td>
                                                <td class="font-weight-bold">Date of Birth</td>
                                                <td>
                                                    <?php echo Dates::formatDateWithRE($membersOfficersArray[$h]['memberDOB'], 'YMD', 'm/d/Y'); ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="font-weight-bold">Credit Score</td>
                                                <td>
                                                    <?php echo $membersOfficersArray[$h]['memberCreditScore']; ?>
                                                </td>
                                                <td class="font-weight-bold">Email</td>
                                                <td><?php echo $membersOfficersArray[$h]['memberEmail']; ?></td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </tr>
                                        <?php } ?>
                                    </table>
                                </td>
                            </tr>
                        <?php } ?>
                        <tr>
                            <td width="25%" class="font-weight-bold">Notes</td>
                            <td width="75%">
                                    <textarea name="entityNotes" rows="3" cols="36" class="form-control"
                                              tabindex="<?php echo $tabIndex++; ?>"><?php echo $entityNotes ?></textarea>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php
    $k++;
}
?>

<div class="modal fade entityInfoContent" id="entityInfoContent" role="dialog">
    <div class="modal-dialog modal-xl" style="max-width: 90% !important;">
        <!-- Modal content-->
        <form name="entityInfoForm" id="entityInfoForm" method="POST" action="<?php echo $saveUrl; ?>"  enctype="multipart/form-data" >
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Entity Info</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <?php
                    $hideThisField = 1; // default assign to show field
                    $isBorrowerProfile = 1;
                    require 'businessEntitySection.php';
                    ?>
                </div>
                <div class="modal-footer">
                    <button type="submit" name='butSubmit' value='Save' class="btn btn-primary">Save</button>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- Modal content End-->
<script type="text/javascript">

    function entityInfoPopup(encCID, encCBEID, encPCID, entitySectionID) {
        var datearray = datearray1 = datearray2 = [];
        clear_form_elements('entityInfoContent');
        $('#statesRegisterdIn').val('');
        $("#statesRegisterdIn option:selected").prop("selected", false);
        $("#statesRegisterdIn").trigger("chosen:updated");
        $('#minTimeInBusiness').val('');
        $("#minTimeInBusiness option:selected").prop("selected", false);
        $("#minTimeInBusiness").trigger("chosen:updated");
        $('#operatingStates').val('');
        $("#operatingStates option:selected").prop("selected", false);
        $("#operatingStates").trigger("chosen:updated");
        $('.hasBusinessBankruptcyExpl').hide();
        $('#encCID').val(encCID);
        $('#encCBEID').val(encCBEID);
        $('#encPCID').val(encPCID);
        $('#entitySectionID').val(entitySectionID);

        //NestedEntityMembers
        $('#allowNestedEntityMembers').val('<?php echo $allowNestedEntityMembers;?>');

        jQuery(".memOff").each(function (i) {
            if (i > 0) $(this).hide();
        });

        if (encCBEID != '' || encCBEID > 0) {
            $.ajax({
                type: 'POST',
                url: '../backoffice/getEntityInfo.php',
                data: jQuery.param({'encCID': encCID, 'encPCID': encPCID, 'encCBEID': encCBEID}),
                contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                success: function (myData) {


                    let obj = $.parseJSON(myData);
                    let ent = obj.entityInfo;
                    let mem = obj.membersInfo;
                    let CBEID = ent.CBEID;
                    //Adjust the form based on the Borrower Type
                    showHideBusinessEntityFields(ent.borrowerType);
                    assignFieldValue(ent.borrowerType, 'borrowerType');
                    assignFieldValue(ent.entityName, 'entityName');
                    assignFieldValue(ent.entityType, 'entityType');
                    assignFieldValue(ent.ENINo, 'ENINo');
                    assignFieldValue(ent.entityAddress, 'entityAddress');
                    assignFieldValue(ent.entityCity, 'entityCity');
                    assignFieldValue(ent.entityState, 'entityState');
                    assignFieldValue(ent.entityZip, 'entityZip');
                    assignFieldValue(ent.entityStateOfFormation, 'entityStateOfFormation');
                    assignFieldValue(ent.entityWebsite, 'entityWebsite');
                    assignFieldValue(ent.entityNotes, 'entityNotes');
                    assignFieldValue(ent.organizationalRef, 'organizationalRef');
                    assignFieldValue(ent.tradeName, 'tradeName');
                    assignFieldValue(ent.businessCategory, 'businessCategory');
                    assignFieldValue(ent.productTypeOrServiceSold, 'productTypeOrServiceSold');
                    assignFieldValue(ent.terminalOrMakeModel, 'terminalOrMakeModel');
                    assignFieldValue(ent.businessPhone, 'businessPhone');
                    assignFieldValue(ent.entityPropertyOwnerShip, 'entityPropertyOwnerShip');
                    assignFieldValue(ent.landlordMortagageContactName, 'landlordMortagageContactName');
                    assignFieldValue(ent.landlordMortagagePhone, 'landlordMortagagePhone');
                    assignFieldValue(ent.rentMortagagePayment, 'rentMortagagePayment');
                    assignFieldValue(ent.avgMonthlyCreditcardSale, 'avgMonthlyCreditcardSale');
                    assignFieldValue(ent.avgTotalMonthlySale, 'avgTotalMonthlySale');
                    assignFieldValue(ent.annualGrossSales, 'annualGrossSales');
                    assignFieldValue(ent.annualGrossProfit, 'annualGrossProfit');
                    assignFieldValue(ent.ordinaryBusinessIncome, 'ordinaryBusinessIncome');
                    assignFieldValue(ent.businessDescription, 'businessDescription');
                    assignFieldValue(ent.merchantProcessingBankName, 'merchantProcessingBankName');
                    assignFieldValue(ent.grossAnnualRevenues, 'grossAnnualRevenues');
                    assignFieldValue(ent.grossIncomeLastYear, 'grossIncomeLastYear');
                    assignFieldValue(ent.netIncomeLastYear, 'netIncomeLastYear');
                    assignFieldValue(ent.grossIncome2YearsAgo, 'grossIncome2YearsAgo');
                    assignFieldValue(ent.netIncome2YearsAgo, 'netIncome2YearsAgo');
                    assignFieldValue(ent.crossCorporateGuarantor, 'crossCorporateGuarantor');
                    assignFieldValue(ent.noOfEmployees, 'noOfEmployees');
                    assignFieldValue(ent.benCardProcessorBank, 'benCardProcessorBank');
                    assignFieldValue(ent.benEmployeesPaid, 'benEmployeesPaid');
                    assignFieldValue(ent.benHowManyLocation, 'benHowManyLocation');
                    assignFieldValue(ent.benOtherLocation, 'benOtherLocation');
                    assignFieldValue(ent.benNameOfFranchise, 'benNameOfFranchise');
                    assignFieldValue(ent.benPointOfContact, 'benPointOfContact');
                    assignFieldValue(ent.benPointOfContactPhone, 'benPointOfContactPhone');
                    assignFieldValue(ent.benPointOfContactEmail, 'benPointOfContactEmail');
                    assignFieldValue(ent.benWebsiteForFranchise, 'benWebsiteForFranchise');
                    assignFieldValue(ent.averageBankBalance, 'averageBankBalance');
                    assignFieldValue(ent.isBusinessSeasonalPeakMonth, 'isBusinessSeasonalPeakMonth');
                    assignFieldValue(ent.noOfEmployeesAfterLoan, 'noOfEmployeesAfterLoan');
                    assignFieldValue(ent.naicsCode, 'naicsCode');

                    assignFieldValue(ent.statesLicIssued, 'statesLicIssued');
                    assignFieldValue(ent.dbaNames, 'dbaNames');
                    assignFieldValue(ent.priorBusName, 'priorBusName');
                    assignFieldValue(ent.entityAddress2, 'entityAddress2');
                    assignFieldValue(ent.businessType, 'businessType');

                    showHideBusinessEntityFields(ent.borrowerType);

                    var dob = ent.dateOfFormation;
                    if (dob != "" && dob != null) {
                        datearray = dob.split("-");
                        var newdate = datearray[1] + '/' + datearray[2] + '/' + datearray[0];
                    } else {
                        var newdate = "";
                    }
                    assignFieldValue(newdate, 'dateOfFormation');
                    var doa = ent.dateOfOperatingAgreement;
                    if (doa != "" && doa != null) {
                        datearray1 = doa.split("-");
                        var newdate1 = datearray1[1] + '/' + datearray1[2] + '/' + datearray1[0];
                    } else {
                        var newdate1 = "";
                    }
                    assignFieldValue(newdate, 'dateOfOperatingAgreement');
                    var sda = ent.startDateAtLocation;
                    if (sda != "" && sda != null) {
                        datearray2 = sda.split("-");
                        var newdate2 = datearray2[1] + '/' + datearray2[2] + '/' + datearray2[0];
                    } else {
                        var newdate2 = "";
                    }
                    assignFieldValue(newdate2, 'startDateAtLocation');
                    assigndDateFieldValue(ent.borLicenseIssuance, 'borLicenseIssuance');
                    assigndDateFieldValue(ent.borLicenseExpiration, 'borLicenseExpiration');

                    assignFieldValue(ent.valueOfProperty, 'valueOfProperty');
                    assignFieldValue(ent.totalDebtOnProperty, 'totalDebtOnProperty');
                    assignFieldValue(ent.nameOfLenders, 'nameOfLenders');
                    assignFieldValue(ent.borLicenseNumber, 'borLicenseNumber');
                    assignFieldValue(ent.estimatedAmountOwed, 'estimatedAmountOwed');

                    var val = ent.entityPropertyOwnerShip;
                    showHideOwnershipChildFields(val);
                    //checkboxes buttons
                    var entityService = ent.entityService;
                    if (entityService == 'Service') {
                        //$('#'+entityService).attr('checked', true);
                        $("input[value='" + entityService + "']").prop('checked', true);
                    }

                    var entityProduct = ent.entityProduct;
                    if (entityProduct == 'Product') {
                        //$('#'+entityProduct).attr('checked', true);
                        $("input[value='" + entityProduct + "']").prop('checked', true);
                    }

                    var entityB2B = ent.entityB2B;
                    if (entityB2B == 'B2B') {
                        //$('#'+entityB2B).attr('checked', true);
                        $("input[value='" + entityB2B + "']").prop('checked', true);
                    }
                    var entityB2C = ent.entityB2C;
                    if (entityB2C == 'B2C') {
                        //$('#'+entityB2C).attr('checked', true);
                        $("input[value='" + entityB2C + "']").prop('checked', true);
                    }
                    var entityLocation = ent.entityLocation;
                    if (entityLocation != '') {
                        $("input[value='" + entityLocation + "']").prop('checked', true);
                    }

                    var isBusinessSeasonalVal = ent.isBusinessSeasonal;
                    if (isBusinessSeasonalVal != '') {
                        $("input[id='isBusinessSeasonal" + isBusinessSeasonalVal + "']").prop('checked', true);
                    }

                    var benBusinessHomeBasedVal = ent.benBusinessHomeBased;
                    if (benBusinessHomeBasedVal != '') {
                        $("input[id='benBusinessHomeBased" + benBusinessHomeBasedVal + "']").prop('checked', true);
                    }

                    var benCreditCardPaymentsVal = ent.benCreditCardPayments;
                    if (benCreditCardPaymentsVal != '') {
                        $("input[id='benCreditCardPayments" + benCreditCardPaymentsVal + "']").prop('checked', true);
                    }

                    var benChargeSalesTaxVal = ent.benCreditCardPayments;
                    if (benChargeSalesTaxVal != '') {
                        $("input[id='benChargeSalesTax" + benChargeSalesTaxVal + "']").prop('checked', true);
                    }
                    var benBusinessLocationVal = ent.benBusinessLocation;
                    if (benBusinessLocationVal != '') {
                        $("input[id='benBusinessLocation" + benBusinessLocationVal + "']").prop('checked', true);
                    }

                    var benBusinessFranchiseVal = ent.benBusinessFranchise;
                    if (benBusinessFranchiseVal != '') {
                        $("input[id='benBusinessFranchise" + benBusinessFranchiseVal + "']").prop('checked', true);
                    }
                    if (ent.hasBusinessBankruptcy) {
                        if (ent.hasBusinessBankruptcy == 'Yes') {
                            $('.hasBusinessBankruptcyExpl').show();
                        }
                        $("input[id='hasBusinessBankruptcy" + ent.hasBusinessBankruptcy + "']").prop('checked', true);
                    }
                    if (ent.businessOweTaxesPrior) {
                        $("input[id='businessOweTaxesPrior" + ent.businessOweTaxesPrior + "']").prop('checked', true);
                    }

                    if (ent.statesRegisterdIn) {
                        let statesRegisterdIn_array = ent.statesRegisterdIn.split(',');
                        $('#statesRegisterdIn').val(statesRegisterdIn_array).trigger("chosen:updated");
                    }

                    if (ent.operatingStates) {
                        let operatingStates_array = ent.operatingStates.split(',');
                        $('#operatingStates').val(operatingStates_array).trigger("chosen:updated");
                    }


                    $('#minTimeInBusiness').val(ent.minTimeInBusiness).trigger("chosen:updated");
                    $('#businessBankruptcyBEN').val(ent.businessBankruptcy).trigger("chosen:updated");
                    $('#recentNSFs').val(ent.recentNSFs).trigger("chosen:updated");

                    //radio buttons
                    var entityLocation = ent.entityLocation;
                    $("input[name=entityLocation][value=" + entityLocation + "]").attr('checked', 'checked');
                    var benBusinessHomeBased = ent.benBusinessHomeBased;
                    $('#benBusinessHomeBased' + benBusinessHomeBased).attr('checked', true);
                    var benCreditCardPayments = ent.benCreditCardPayments;
                    $('#benCreditCardPayments' + benCreditCardPayments).attr('checked', true);
                    showHideCreditCardFields(benCreditCardPayments);
                    var benChargeSalesTax = ent.benChargeSalesTax;
                    $("input[name=benChargeSalesTax][value=" + benChargeSalesTax + "]").attr('checked', 'checked');
                    var benBusinessLocation = ent.benBusinessLocation;
                    $('#benBusinessLocation' + benBusinessLocation).attr('checked', true);
                    showHideBusinessLocation(benBusinessLocation);
                    var benBusinessFranchise = ent.benBusinessFranchise;
                    $('#benBusinessFranchise' + benBusinessFranchise).attr('checked', true);
                    showHideBusinessFranchise(benBusinessFranchise);
                    var isBusinessSeasonal = ent.isBusinessSeasonal;
                    $('#isBusinessSeasonal' + isBusinessSeasonal).attr('checked', true);
                    hideAndShowSection(isBusinessSeasonal, 'Yes', 'isBusinessSeasonalPeakMonth_disp');
                    if (<?php echo $allowNestedEntityMembers;?>) { //Nested Entity Members - Enabled
                        if (!jQuery.isEmptyObject(mem)) {
                            let memData = mem[CBEID] ?? [];
                            let memCount = $('#parentCount').val();
                            if (memCount > 0) {
                                $('#parentEntityMemberCount').val(memCount);
                            }
                        }
                        borrowerProfile.loadEntityInfoMembers();
                    } else { //Nested Entity Members - Disabled | Old Code
                        if (!jQuery.isEmptyObject(mem)) {
                            var mOffice = mem[CBEID];
                            var ks = 0;
                            for (var i = 0; i < mOffice.length; i++) {
                                $('.MembersOfficers' + ks + 'ID').show();
                                assignFieldValue(mOffice[i].memberName, 'memberName' + ks);
                                assignFieldValue(mOffice[i].memberTitle, 'memberTitle' + ks);
                                assignFieldValue(mOffice[i].memberOwnership, 'memberOwnership' + ks);
                                assignFieldValue(mOffice[i].memberAnnualSalary, 'memberAnnualSalary' + ks);
                                assignFieldValue(mOffice[i].memberAddress, 'memberAddress' + ks);
                                assignFieldValue(mOffice[i].memberPhone, 'memberPhone' + ks);
                                assignFieldValue(mOffice[i].memberCell, 'memberCell' + ks);
                                assignFieldValue(mOffice[i].memberSSN, 'memberSSN' + ks);
                                assignFieldValue(mOffice[i].memberCreditScore, 'memberCreditScore' + ks);
                                assignFieldValue(mOffice[i].memberEmail, 'memberEmail' + ks);
                                assignFieldValue(mOffice[i].memberDriversLicense, 'memberDriversLicense' + ks);
                                assignFieldValue(mOffice[i].memberDriversLicenseState, 'memberDriversLicenseState' + ks);
                                assignFieldValue(mOffice[i].memberTin, 'memberTin' + ks);
                                assignFieldValue(mOffice[i].memberMaidenName, 'memberMaidenName' + ks);
                                assignFieldValue(mOffice[i].memberSpouseName, 'memberSpouseName' + ks);
                                assignFieldValue(mOffice[i].memberRentOrOwn, 'memberRentOrOwn' + ks);
                                assignFieldValue(mOffice[i].memberMonthlyRentOrMortgage, 'memberMonthlyRentOrMortgage' + ks);
                                assignFieldValue(mOffice[i].memberRealEstateValue, 'memberRealEstateValue' + ks);
                                assignFieldValue(mOffice[i].memberRetirementAccountBalance, 'memberRetirementAccountBalance' + ks);
                                assignFieldValue(mOffice[i].memberCashSavingsStocksBalance, 'memberCashSavingsStocksBalance' + ks);
                                assignFieldValue(mOffice[i].memberCreditCardBalance, 'memberCreditCardBalance' + ks);
                                assignFieldValue(mOffice[i].memberMortgageBalance, 'memberMortgageBalance' + ks);
                                assignFieldValue(mOffice[i].memberAutoLoanBalance, 'memberAutoLoanBalance' + ks);
                                assignFieldValue(mOffice[i].memberTotalNetWorth, 'memberTotalNetWorth' + ks);

                                //Dates
                                let memberDOB = mOffice[i].memberDOB;
                                if (memberDOB) {
                                    let memberDOBArray = memberDOB.split("-");
                                    memberDOB = memberDOBArray[1] + '/' + memberDOBArray[2] + '/' + memberDOBArray[0];
                                } else {
                                    memberDOB = '';
                                }
                                assignFieldValue(memberDOB, 'memberDOB' + ks);

                                let memberMarriageDate = mOffice[i].memberMarriageDate;
                                if (memberMarriageDate) {
                                    let memberMarriageDateArray = memberMarriageDate.split("-");
                                    memberMarriageDate = memberMarriageDateArray[1] + '/' + memberMarriageDateArray[2] + '/' + memberMarriageDateArray[0];
                                } else {
                                    memberMarriageDate = '';
                                }
                                assignFieldValue(memberMarriageDate, 'memberMarriageDate' + ks);

                                let memberDivorceDate = mOffice[i].memberDivorceDate;
                                if (memberDivorceDate) {
                                    let memberDivorceDateArray = memberDivorceDate.split("-");
                                    memberDivorceDate = memberDivorceDateArray[1] + '/' + memberDivorceDateArray[2] + '/' + memberDivorceDateArray[0];
                                } else {
                                    memberDivorceDate = '';
                                }
                                assignFieldValue(memberDivorceDate, 'memberDivorceDate' + ks);

                                let memberCreditScoreDate = mOffice[i].memberCreditScoreDate;
                                if (memberCreditScoreDate) {
                                    let memberCreditScoreDateArray = memberCreditScoreDate.split("-");
                                    memberCreditScoreDate = memberCreditScoreDateArray[1] + '/' + memberCreditScoreDateArray[2] + '/' + memberCreditScoreDateArray[0];
                                } else {
                                    memberCreditScoreDate = '';
                                }
                                assignFieldValue(memberCreditScoreDate, 'memberCreditScoreDate' + ks);

                                let memberDateMovedAddress = mOffice[i].memberDateMovedAddress;
                                if (memberDateMovedAddress) {
                                    let memberDateMovedAddressArray = memberDateMovedAddress.split("-");
                                    memberDateMovedAddress = memberDateMovedAddressArray[1] + '/' + memberDateMovedAddressArray[2] + '/' + memberDateMovedAddressArray[0];
                                } else {
                                    memberDateMovedAddress = '';
                                }
                                assignFieldValue(memberDateMovedAddress, 'memberDateMovedAddress' + ks);
                                //Radio Buttons
                                let memberPersonalGuarantee = mOffice[i].memberPersonalGuarantee;
                                $("input[type='radio'][name='memberPersonalGuarantee_" + ks + "'][value='" + memberPersonalGuarantee + "']").prop("checked", true);

                                let memberAuthorizedSigner = mOffice[i].memberAuthorizedSigner;
                                $("input[type='radio'][name='memberAuthorizedSigner_" + ks + "'][value='" + memberAuthorizedSigner + "']").prop("checked", true);

                                let memberCitizenship = mOffice[i].memberCitizenship;
                                $("input[type='radio'][name='memberCitizenship_" + ks + "'][value='" + memberCitizenship + "']").prop("checked", true);

                                let memberMaritalStatus = mOffice[i].memberMaritalStatus;
                                $("input[type='radio'][name='memberMaritalStatus_" + ks + "'][value='" + memberMaritalStatus + "']").prop("checked", true);

                                ks++;
                            }
                            // masking the data
                            $(".mask_phone:enabled").inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
                            $(".mask_cell:enabled").inputmask("mask", {mask: "999 - 999 - 9999"});
                            $(".mask_ssn:enabled").inputmask("999 - 99 - 9999", {
                                placeholder: "___ - __ - ____",
                                clearMaskOnLostFocus: !0
                            });
                        }
                    }
                }
            });
        } else {
            borrowerProfile.loadEntityInfoMembers();
        }
        $('#entityInfoContent').modal('show');
    }

    class borrowerProfile {
        static getEntityInfoMembers(_element) {
            let number = parseFloat($(_element).val());
            if (number > 10) {
                toastrNotification('Please enter a valid number and less than or equal to 10', 'error');
                $(_element).val(''); //reset the value
                return false;
            }
            let row = $(_element).data('row');
            let inc = $(_element).data('inc');
            let encCID = $(_element).data('cid');
            let encCBEID = $(_element).data('cbeid');
            let moid = $(_element).data('moid');
            let formContainerId = $(_element).attr('id');
            if(formContainerId === 'parentEntityMemberCount') {
                formContainerId = '1_1';
            }
            let formContainer;
            if(row === 1) {
                formContainer = 'entityMembersDiv_' + formContainerId;
            } else {
                formContainer = 'formContainer_' + formContainerId;
            }
            let encPCID = $('#encPCID').val();
            HTTP.Get('/backoffice/borrowerProfile/', { //new data
                    CID: encCID,
                    CBEID: encCBEID,
                    MOID: moid,
                    number: number,
                    encPCID:encPCID,
                }, function (data) {
                    let html = data.html;
                    /*if (row === 1) {
                        $('#' + formContainer).empty().append(html);
                    } else {
                        $('#' + formContainer).empty().append(html);
                    }*/
                $('#' + formContainer).empty().append(html);
                // Reapply the input mask to the newly appended input fields
                $('.mask_phone:enabled').inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
                $('.mask_ssn:enabled').inputmask("mask", {mask: "***********"});
                $('.mask_cellnew:enabled').inputmask("mask", {mask: "(999) 999 - 9999"});
                $('.mask_ein:enabled').inputmask("mask", {mask: "99-9999999"});
                $('.memberCreditScore:enabled').inputmask({
                    mask: "999",
                    placeholder: '',
                });
                // Initialize tooltips for the newly added elements
                $('#' + formContainer + ' [data-toggle="tooltip"]').tooltip();
                }
            );
        }

        static loadEntityInfoMembers() {
            let encCID = $('#encCID').val();
            let encCBEID = $('#encCBEID').val();
            let encPCID = $('#encPCID').val();
            HTTP.Post('/backoffice/borrowerProfile/profile/loadBorrowerProfile', {
                    CID: encCID,
                    CBEID: encCBEID,
                    encPCID: encPCID,
                }, function (data) {
                    if (data.html) {
                        $('#entityMembersDiv_1_1').empty().html(data.html);
                        $('.mask_phone:enabled').inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
                        $('.mask_ssn:enabled').inputmask("mask", {mask: "***********"});
                        $('.mask_cellnew:enabled').inputmask("mask", {mask: "(999) 999 - 9999"});
                        $('.mask_ein:enabled').inputmask("mask", {mask: "99-9999999"});
                    }
                    if (data.uploadHTML) {
                        $('#entityDocs_1_1').empty().html(data.uploadHTML);
                    }
                }
            );
        }

        static removeEntityMember(obj) {
            let rowId = $(obj).attr('data-id');
            let rowInc = $(obj).attr('data-entityMemberInc');
            let rowEncVal = $(obj).attr('data-enc-id');
            let memId = $(obj).attr('data-memberId'); //loop 1-5 each member
            let entId = $(obj).attr('data-entityMemberId');
            let rowRemove = $(obj).attr('data-remove');
            let RowId = $(obj).attr('data-RowId');
            if(rowId) {
                let conf = 'Are you sure to delete?';
                $.confirm({
                    icon: 'fas fa-exclamation-triangle text-danger',
                    closeIcon: true,
                    title: 'Confirm',
                    content: conf,
                    type: 'red',
                    backgroundDismiss: true,
                    buttons: {
                        yes: function () {
                            if(RowId !== '0') { //delete from db
                                HTTP.Post('/backoffice/borrowerProfile', {
                                        rowId: rowEncVal,
                                    }, function (response) {
                                        if (response.success) {
                                            if(rowInc === '1') { // 1
                                                borrowerProfile.clearEntityMemberFields(rowRemove);
                                            } else {
                                                $('#entityMemberDiv_' + memId + '_' + entId).remove();
                                            }
                                            toastrNotification(response.success, 'success');
                                        } else {
                                            toastrNotification(response.error, 'error');
                                        }
                                    }
                                );
                            } else { //delete from UI
                                if(rowInc === '1') { // 1
                                    borrowerProfile.clearEntityMemberFields(rowRemove);
                                } else {
                                    $('#entityMemberDiv_' + memId + '_' + entId).remove();
                                }
                                toastrNotification('Deleted Successfully', 'success');
                            }

                        }, cancel: function () {

                        },
                    },
                    onClose: function () {

                    },
                });
            }
        }

        static clearEntityMemberFields(rowRemove)
        {
            $('#entityMemberName_' + rowRemove).val('');
            $('#entityMemberTitle_' + rowRemove).val('');
            $('#entityMemberOwnership_' + rowRemove).val('');
            $('#entityMemberPhone_' + rowRemove).val('');
            $('#entityMemberCell_' + rowRemove).val('');
            $('#entityMemberSSN_' + rowRemove).val('');
            $('#entityMemberDOB_' + rowRemove).val('');
            $('#entityMemberEmail_' + rowRemove).val('');
            //checkboxes
            let exp = rowRemove.split('_');
            let incId1 = exp[0];
            let incId2 = exp[1];
            $('input[type=radio][name="members[' + incId1 + '][entityMember][' + incId2 + '][entityMemberCitizenship]"]').prop('checked', false);
            $('input[type=radio][name="members[' + incId1 + '][entityMember][' + incId2 + '][entityMemberPersonalGuarantee]"]').prop('checked', false);
            $('input[type=radio][name="members[' + incId1 + '][entityMember][' + incId2 + '][entityMemberAuthorizedSigner]"]').prop('checked', false);
        }

        static removeEntityMemberData(_this) {
            let accordion = $(_this).data('rem-accordion');
            let pkId = $(_this).data('rem-tableid');
            let cid  = $(_this).data('rem-cid');
            let cbeid = $(_this).data('rem-cbeid');
            let accordionDiv = $('#' + accordion);
            if(pkId) { // remove from the db
                let conf = 'Are you sure to delete?';
                $.confirm({
                    icon: 'fas fa-exclamation-triangle text-danger',
                    closeIcon: true,
                    title: 'Confirm',
                    content: conf,
                    type: 'red',
                    backgroundDismiss: true,
                    buttons: {
                        yes: function () {
                            HTTP.Post('/backoffice/borrowerProfile/profile/remove_member',
                                {
                                    cid: cid,
                                    cbeid: cbeid,
                                    tableId: pkId,
                                },
                                function (response) {
                                    if (response.success) {
                                        // Dispose of any tooltips within the card to be deleted
                                        accordionDiv.find('[data-toggle="tooltip"]').tooltip('dispose');
                                        accordionDiv.remove();
                                        toastrNotification(response.success, 'success');
                                    } else {
                                        toastrNotification(response.error, 'error');
                                    }
                                });
                        }, cancel: function () {

                        },
                    },
                    onClose: function () {

                    },
                });
            } else { // remove from the ui
                // Dispose of any tooltips within the card to be deleted
                accordionDiv.find('[data-toggle="tooltip"]').tooltip('dispose');
                accordionDiv.remove();
            }
        }
    }
</script>
<script>
    $(function () {
        let entityInfoForm = $('#entityInfoForm');
        entityInfoForm.submit(function (e) {
            e.preventDefault();
            let borrowerTypeField = $('#borrowerType');
            let borrowerType = borrowerTypeField.val();
            if (!borrowerType) {
                toastrNotification('Please select Borrower Type', 'error');
                return false;
            }
            document.getElementById('entityInfoForm').submit();
        });
    });
</script>
