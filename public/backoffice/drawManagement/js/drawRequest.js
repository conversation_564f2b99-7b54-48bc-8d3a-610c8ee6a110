/**
 * Common Draw Request Functions
 * Shared functionality between borrower and lender draw request forms
 */
window.DrawRequestUtils = window.DrawRequestUtils || {};

DrawRequestUtils.calculateAmountFromPercent = function($percentInput) {
    const percent = parseFloat($percentInput.val()) || 0;
    const cost = parseFloat($percentInput.data('cost')) || 0;
    const requestedAmount = (percent / 100) * cost;

    return Math.max(0, requestedAmount);
};

DrawRequestUtils.calculatePercentFromAmount = function($amountInput) {
    const amount = parseFloat($amountInput.val()) || 0;
    const cost = parseFloat($amountInput.data('cost')) || 0;

    const percent = cost > 0 ? (amount / cost) * 100 : 0;
    return Math.max(0, Math.min(100, percent));
};

DrawRequestUtils.validatePercentInput = function($percentInput) {
    const percent = parseFloat($percentInput.val()) || 0;
    const completedPercent = parseFloat($percentInput.data('completed-percent')) || 0;
    const maxPercent = 100 - completedPercent;

    const $validationMsg = $percentInput.closest('td').find('.validation-message');

    if (percent < 0) {
        $validationMsg.text('Percentage cannot be negative').show();
        $percentInput.addClass('is-invalid');
        return false;
    } else if (percent > maxPercent) {
        $validationMsg.text(`Percentage cannot exceed ${maxPercent.toFixed(2)}% (100% - ${completedPercent.toFixed(2)}% completed)`).show();
        $percentInput.addClass('is-invalid');
        return false;
    } else {
        $validationMsg.hide();
        $percentInput.removeClass('is-invalid');
        return true;
    }
};

DrawRequestUtils.validateAmountInput = function($amountInput) {
    const amount = parseFloat($amountInput.val()) || 0;
    const cost = parseFloat($amountInput.data('cost')) || 0;
    const completedAmount = parseFloat($amountInput.data('completed-amount')) || 0;
    const maxAmount = cost - completedAmount;

    const $validationMsg = $amountInput.closest('td').find('.validation-message');

    if (amount < 0) {
        $validationMsg.text('Amount cannot be negative').show();
        $amountInput.addClass('is-invalid');
        return false;
    } else if (amount > maxAmount) {
        $validationMsg.text(`Amount cannot exceed $${maxAmount.toFixed(2)} (Total Budget - Completed Renovations)`).show();
        $amountInput.addClass('is-invalid');
        return false;
    } else {
        $validationMsg.hide();
        $amountInput.removeClass('is-invalid');
        return true;
    }
};

DrawRequestUtils.getPercentageColor = function(percentage) {
    const p = Math.max(0, Math.min(100, percentage));

    if (p < 25) {
        return 'bg-danger';
    } else if (p < 50) {
        return 'bg-info';
    } else if (p < 75) {
        return 'bg-primary';
    } else {
        return 'bg-success';
    }
};

DrawRequestUtils.initializePercentageColors = function() {
    $('.percentage').each(function() {
        const percentage = parseInt($(this).text());
        $(this).addClass(DrawRequestUtils.getPercentageColor(percentage));
    });
};

DrawRequestUtils.setupInputHandlers = function(additionalValidationCallback) {
    $('.requested-percent').on('input', function() {
        const $percentInput = $(this);
        const lineItemId = $percentInput.data('line-item-id');
        const $amountInput = $(`.requested-amount[data-line-item-id="${lineItemId}"]`);

        if (DrawRequestUtils.validatePercentInput($percentInput)) {
            const calculatedAmount = DrawRequestUtils.calculateAmountFromPercent($percentInput);
            $amountInput.val(calculatedAmount.toFixed(2));
            DrawRequestUtils.validateAmountInput($amountInput);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });

    $('.requested-amount').on('input', function() {
        const $amountInput = $(this);
        const lineItemId = $amountInput.data('line-item-id');
        const $percentInput = $(`.requested-percent[data-line-item-id="${lineItemId}"]`);

        if (DrawRequestUtils.validateAmountInput($amountInput)) {
            const calculatedPercent = DrawRequestUtils.calculatePercentFromAmount($amountInput);
            $percentInput.val(calculatedPercent.toFixed(2));
            DrawRequestUtils.validatePercentInput($percentInput);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });

    $('.requested-percent, .requested-amount').on('blur', function() {
        const $input = $(this);
        if ($input.hasClass('requested-percent')) {
            DrawRequestUtils.validatePercentInput($input);
        } else {
            DrawRequestUtils.validateAmountInput($input);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });
};

DrawRequestUtils.validateAllInputs = function() {
    let isValid = true;

    $('.requested-percent').each(function() {
        if (!DrawRequestUtils.validatePercentInput($(this))) {
            isValid = false;
        }
    });

    $('.requested-amount').each(function() {
        if (!DrawRequestUtils.validateAmountInput($(this))) {
            isValid = false;
        }
    });

    return isValid;
};

DrawRequestUtils.hasNonZeroValues = function() {
    let hasNonZeroValue = false;

    $('.requested-percent, .requested-amount').each(function() {
        const value = parseFloat($(this).val()) || 0;
        if (value > 0) {
            hasNonZeroValue = true;
            return false;
        }
    });

    return hasNonZeroValue;
};

DrawRequestUtils.markFunded = function() {
    $('span.percentage').each(function() {
        const $percentageElement = $(this);
        const percentage = parseInt($percentageElement.text());

        if (percentage === 100) {
            const $row = $percentageElement.closest('tr.line-item');
            $row.addClass('funded');
            $row.find('.requested-percent').prop('disabled', true);
            $row.find('.requested-amount').prop('disabled', true);
            $row.css('background', 'linear-gradient(to right, #5ac272ff 0%, #fff 100%)');
        }
    });
};

DrawRequestUtils.hasValidationErrors = function() {
    return $('.is-invalid').length > 0;
};

$(document).ready(function() {
    DrawRequestUtils.initializePercentageColors();
    DrawRequestUtils.markFunded();
});
