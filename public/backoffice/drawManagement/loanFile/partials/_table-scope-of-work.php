<table class="table table-hover table-bordered table-vertical-center">
    <thead>
        <tr>
            <th style="width: 400px;">Line Item</th>
            <th>Total Budget</th>
            <th>Completed Renovations</th>
            <th>% Completed</th>
            <th style="width: 70px;">Borrower Notes</th>
            <th style="width: 70px;">Lender Notes</th>
            <th style="width: 200px;" class="hide col-reject-reason">Reject Reason</th>
        </tr>
    </thead>
    <tbody>
        <?php if (!empty($categoriesData)): ?>
            <?php foreach ($categoriesData as $category): ?>
                <?php if (!empty($category->getAllLineItems())): ?>
                    <tr class="category-header">
                        <td colspan="7">
                            <?= htmlspecialchars(strtoupper($category->categoryName)) ?>
                            <?php if (!empty($category->description)): ?>
                            <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                title="<?= htmlspecialchars($category->description) ?>"></i>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php if (!empty($category->getAllLineItems())): ?>
                        <?php foreach ($category->getAllLineItems() as $lineItem): ?>
                            <tr class="line-item">
                                <td>
                                    <?= htmlspecialchars($lineItem->name) ?>
                                    <?php if (!empty($lineItem->description)): ?>
                                        <i class="fa fa-info-circle text-primary tooltipClass ml-2" title="<?= htmlspecialchars($lineItem->description) ?>"></i>
                                    <?php endif; ?>
                                </td>
                                <td>$<?= number_format($lineItem->cost, 2) ?></td>
                                <td>$<?= number_format($lineItem->completedAmount, 2) ?></td>
                                <td>
                                    <span class="badge percentage"><?= round($lineItem->completedPercent) ?>%</span>
                                </td>
                                <td>
                                    <button class="btn note-btn btn-sm" type="button">
                                        <i class="icon-md fas fa-comment-medical fa-lg tooltipClass <?= !empty($lineItem->notes) ? 'text-primary' : 'text-muted' ?>"
                                            data-original-title="<?= htmlspecialchars($lineItem->notes) ?>">
                                        </i>
                                        <input type="hidden" name="notes" value="<?= htmlspecialchars($lineItem->notes) ?>">
                                    </button>
                                </td>
                                <td>
                                    <button class="btn lender-note-btn btn-sm" type="button"
                                        data-line-item-id="<?= $lineItem->id ?>">
                                        <i class="icon-md fas fa-comment-medical fa-lg tooltipClass <?= !empty($lineItem->lenderNotes) ? 'text-primary' : 'text-muted' ?>"
                                            data-original-title="<?= htmlspecialchars($lineItem->lenderNotes) ?>">
                                        </i>
                                    </button>
                                    <input type="hidden" name="lenderNotes" value="<?= htmlspecialchars($lineItem->lenderNotes) ?>">
                                </td>
                                <td class="hide col-reject-reason">
                                    <select class="form-control input-sm" name="rejectReason">
                                        <option <?= $lineItem->rejectReason === '' ? 'selected' : ''; ?> value="">-- None --</option>
                                        <option <?= $lineItem->rejectReason === 'Revise Budget' ? 'selected' : ''; ?> value="Revise Budget">Revise Budget</option>
                                        <option <?= $lineItem->rejectReason === 'Line Item not covered' ? 'selected' : ''; ?> value="Line Item not covered">Line Item not covered</option>
                                        <option <?= $lineItem->rejectReason === 'Other(See Notes)' ? 'selected' : ''; ?> value="Other(See Notes)">Other(See Notes)</option>
                                    </select>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        <?php else: ?>
        <tr>
            <td colspan="7" class="text-center text-muted py-4">
                No draw request data available.
            </td>
        </tr>
        <?php endif; ?>
    </tbody>
</table>
